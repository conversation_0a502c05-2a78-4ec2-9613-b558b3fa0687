package com.dicernszh.botany.qw

import Choice
import Message
import OcrResponse
import Usage
import android.app.Activity
import android.text.TextUtils
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog
import org.json.JSONArray
import org.json.JSONObject
import okhttp3.*
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import com.dicernszh.botany.Image2TxtResultAct
import com.dicernszh.botany.R
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import java.io.IOException
import java.util.concurrent.TimeUnit

interface OnDetect<T> {
    fun onSuccess(data: T?)
    fun onFail(msg: String?)
    fun onStreamData(data: String) // 流式输出的回调方法
}

class DetectAnything(private var context: Activity, filePath: String, type: Int) {
    private var filePath: String? = filePath
    private var mType: Int = type
    var tipDialog: QMUITipDialog? = null
    private var detectBack: OnDetect<OcrResponse>? = null

    // OkHttp客户端 - 增加超时时间以适应长时间响应
    private val client = OkHttpClient.Builder()
        .connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(120, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    // 保存Call引用，以便在需要时取消请求
    private var currentCall: Call? = null

    // 用于存储流式响应的完整结果
    private val completeResponseBuilder = StringBuilder()

    fun start(detectBack: OnDetect<OcrResponse>?) {
        this.detectBack = detectBack
        if (!TextUtils.isEmpty(filePath)) {
            compressImage(filePath)
        } else {
            detectBack?.onFail("图片不能为空")
        }
    }

    private fun compressImage(resultPath: String?) {
        tipDialog = QMUITipDialog.Builder(context).setTipWord("图片检测中")
            .setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).create()

        Luban.with(context).load(File(resultPath)).setCompressListener(object : OnCompressListener {
            override fun onStart() {
                tipDialog?.show()
            }

            override fun onSuccess(index: Int, file: File) {
                if (mType == Image2TxtResultAct.RESULT_TYPE_OCR) {
                    recognizeDoc(file.absolutePath, context)
                } else {
                    recognizeObject(file.absolutePath, context)
                }
            }

            override fun onError(index: Int, e: Throwable) {
                tipDialog?.dismiss()
                detectBack?.onFail(e.message)
            }
        }).launch()
    }

    private fun recognizeDoc(sourcePath: String, context: Activity) {
        val requestBody = JSONObject().apply {
            put("model", "qwen-vl-ocr")
            put("stream", true) // 启用流式输出
            val messagesArray = JSONArray()
            val messageObject = JSONObject()
            messageObject.put("role", "user")
            val contentArray = JSONArray()
            // Add image
            val imageContent = JSONObject()
            imageContent.put("type", "image_url")
            val imageUrl = JSONObject()
            imageUrl.put("url", "data:image/png;base64,${Base64Utils.getRequestBase64FromFile(sourcePath, context)}")
            imageContent.put("image_url", imageUrl)
            contentArray.put(imageContent)
            // Add text prompt
            val textContent = JSONObject()
            textContent.put("type", "text")
            textContent.put("text", context.getString(R.string.prompt_ocr_doc))
            contentArray.put(textContent)
            messageObject.put("content", contentArray)
            messagesArray.put(messageObject)
            put("messages", messagesArray)
        }
        sendSseRequest(requestBody)
    }

    private fun recognizeObject(sourcePath: String, context: Activity) {
        val requestBody = JSONObject().apply {
            put("model", "qwen-vl-max-latest")
            put("stream", true) // 启用流式输出
            val messagesArray = JSONArray()
            val messageObject = JSONObject()
            messageObject.put("role", "user")
            val contentArray = JSONArray()
            val textContent = JSONObject()
            textContent.put("type", "text")

            when (mType) {
                Image2TxtResultAct.RESULT_TYPE_GENERIC_ALBUM,
                Image2TxtResultAct.RESULT_TYPE_GENERIC_SHOT -> {
                    textContent.put("text", context.getString(R.string.prompt_generic))
                }

                Image2TxtResultAct.RESULT_TYPE_ANIMAL -> {
                    textContent.put("text", context.getString(R.string.prompt_animal))
                }

                Image2TxtResultAct.RESULT_TYPE_FLOWER,
                Image2TxtResultAct.RESULT_TYPE_PLANT -> {
                    textContent.put("text", context.getString(R.string.prompt_plant))
                }

                Image2TxtResultAct.RESULT_TYPE_FRUIT -> {
                    textContent.put("text", context.getString(R.string.prompt_fruit))
                }

                Image2TxtResultAct.RESULT_TYPE_DISH -> {
                    textContent.put("text", context.getString(R.string.prompt_dish))
                }

                Image2TxtResultAct.RESULT_TYPE_LOGO -> {
                    textContent.put("text", context.getString(R.string.prompt_logo))
                }

                Image2TxtResultAct.RESULT_TYPE_LAND -> {
                    textContent.put("text", context.getString(R.string.prompt_landmark))
                }

                Image2TxtResultAct.RESULT_TYPE_MMONEY -> {
                    textContent.put("text", context.getString(R.string.prompt_currency))
                }

                else -> {
                    textContent.put("text", context.getString(R.string.prompt_default))
                }
            }

            contentArray.put(textContent)

            val imageContent = JSONObject()
            imageContent.put("type", "image_url")
            val imageUrl = JSONObject()
            imageUrl.put("url", "data:image/png;base64,${Base64Utils.getRequestBase64FromFile(sourcePath, context)}")
            imageContent.put("image_url", imageUrl)
            contentArray.put(imageContent)

            messageObject.put("content", contentArray)
            messagesArray.put(messageObject)
            put("messages", messagesArray)
        }
        sendSseRequest(requestBody)
    }

    private fun sendSseRequest(requestBody: JSONObject) {
        val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
        val requestBodyOkHttp = RequestBody.create(mediaType, requestBody.toString())

        val request = Request.Builder()
            .url("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
            .addHeader("Authorization", "Bearer sk-f50dc7b090774022b7d917a7ac85a4fb")
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "text/event-stream") // 请求SSE流
            .post(requestBodyOkHttp)
            .build()

        // 保存call引用，以便在需要时取消
        currentCall = client.newCall(request)

        // 执行异步请求
        currentCall?.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                context.runOnUiThread {
                    tipDialog?.dismiss()
                    detectBack?.onFail("请求失败: ${e.message}")
                }
            }

            override fun onResponse(call: Call, response: Response) {
                if (!response.isSuccessful) {
                    context.runOnUiThread {
                        tipDialog?.dismiss()
                        detectBack?.onFail("HTTP 错误: ${response.code}")
                    }
                    return
                }

                // 使用改进的方式处理SSE响应
                handleSseResponse(call, response)
            }
        })
    }

    // 改进的SSE响应处理方法
    private fun handleSseResponse(call: Call, response: Response) {
        val reader = response.body?.charStream()?.buffered()
        if (reader == null) {
            context.runOnUiThread {
                tipDialog?.dismiss()
                detectBack?.onFail("响应体为空")
            }
            return
        }

        try {
            // 使用缓冲区合并短小的内容片段，减少UI更新频率
            val contentBuffer = StringBuilder()
            var line: String? = ""
            var isDone = false

            // 使用bufferedReader逐行读取，更适合处理文本内容
            while (!isDone && !call.isCanceled() && reader.readLine().also { line = it } != null) {
                // 跳过空行
                if (line.isNullOrEmpty()) {
                    continue
                }


                // 处理SSE数据行
                if (line!!.startsWith("data:")) {
                    val data = line!!.substring(5).trim()
                    context.runOnUiThread {
                        tipDialog?.dismiss()
                    }
                    if (data == "[DONE]") {
                        // 流结束标志
                        isDone = true

                        // 发送剩余缓冲区内容
                        if (contentBuffer.isNotEmpty()) {
                            val finalChunk = contentBuffer.toString()
                            contentBuffer.clear()

                            context.runOnUiThread {
                                detectBack?.onStreamData(finalChunk)
                            }
                        }

                        // 通知完成并返回完整结果
                        context.runOnUiThread {
                            tipDialog?.dismiss()
                            val responseObject = buildFinalResponse()
                            detectBack?.onSuccess(responseObject)
                        }
                    } else {
                        try {
                            // 解析JSON数据
                            val jsonData = JSONObject(data)

                            // 处理增量内容
                            if (jsonData.has("choices")) {
                                val choices = jsonData.getJSONArray("choices")
                                if (choices.length() > 0) {
                                    val choice = choices.getJSONObject(0)
                                    if (choice.has("delta")) {
                                        val delta = choice.getJSONObject("delta")
                                        if (delta.has("content")) {
                                            val content = delta.getString("content")

                                            // 添加到完整响应
                                            completeResponseBuilder.append(content)

                                            // 添加到内容缓冲区
                                            contentBuffer.append(content)

                                            // 当缓冲区积累到一定大小，或遇到句末标点时更新UI
                                            if (contentBuffer.length >= 20 ||
                                                content.contains("。") ||
                                                content.contains("\n") ||
                                                content.contains("，") && contentBuffer.length >= 10
                                            ) {

                                                val bufferContent = contentBuffer.toString()
                                                contentBuffer.clear()

                                                context.runOnUiThread {
                                                    detectBack?.onStreamData(bufferContent)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            // JSON解析错误，记录但不中断流处理
                            e.printStackTrace()
                        }
                    }
                }
            }

            // 如果没有正常结束但流已关闭，处理剩余内容
            if (!isDone && !call.isCanceled()) {
                // 发送剩余缓冲区内容
                if (contentBuffer.isNotEmpty()) {
                    val remainingContent = contentBuffer.toString()

                    context.runOnUiThread {
                        detectBack?.onStreamData(remainingContent)
                    }
                }

                // 构建最终响应
                context.runOnUiThread {
                    tipDialog?.dismiss()
                    val finalResponse = buildFinalResponse()
                    if (completeResponseBuilder.isNotEmpty()) {
                        detectBack?.onSuccess(finalResponse)
                    } else {
                        detectBack?.onFail("响应流异常结束")
                    }
                }
            }
        } catch (e: Exception) {
            context.runOnUiThread {
                tipDialog?.dismiss()
                detectBack?.onFail("读取响应流失败: ${e.message}")
            }
        } finally {
            try {
                reader.close()
            } catch (e: Exception) {
                // 忽略关闭异常
            }
            response.close()
        }
    }

    // 构建最终的完整响应对象
    private fun buildFinalResponse(): OcrResponse {
        return OcrResponse(
            choices = listOf(
                Choice(
                    finish_reason = "stop",
                    index = 0,
                    logprobs = null,
                    message = Message(
                        content = completeResponseBuilder.toString(),
                        role = "assistant"
                    )
                )
            ),
            created = (System.currentTimeMillis() / 1000).toInt(),
            id = "chatcmpl-" + System.currentTimeMillis(),
            model = if (mType < 10) "qwen-vl-max-latest" else "qwen-vl-ocr",
            `object` = "chat.completion",
            system_fingerprint = null,
            usage = Usage(
                completion_tokens = 0,
                prompt_tokens = 0,
                total_tokens = 0
            )
        )
    }

    // 取消正在进行的请求
    fun cancel() {
        currentCall?.cancel()
        tipDialog?.dismiss()
    }
}