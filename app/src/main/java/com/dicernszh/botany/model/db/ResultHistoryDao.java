package com.dicernszh.botany.model.db;

import android.content.Context;

import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.stmt.QueryBuilder;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ResultHistoryDao {
    private Dao<ResultHistoryTableBean, Integer> dao = null;

    public ResultHistoryDao(Context context) {
        try {
            this.dao = DatabaseHelper.getInstance(context).getDao(ResultHistoryTableBean.class);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public void clear() {
        try {
            // More efficient way to clear all records
            dao.deleteBuilder().delete();
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public void insertHistoryItem(ResultHistoryTableBean historyTableBean) {
        try {
            // Always insert new record, don't check for existence
            dao.create(historyTableBean);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public void updateHistoryItem(ResultHistoryTableBean historyTableBean) {
        try {
            dao.update(historyTableBean);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public void deleteHistoryItem(int id) {
        try {
            dao.deleteById(id);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public List<ResultHistoryTableBean> queryHistoryByType(int type) {
        List<ResultHistoryTableBean> historyTableList = null;
        try {
            // Query with proper ordering (latest first)
            QueryBuilder<ResultHistoryTableBean, Integer> queryBuilder = dao.queryBuilder();
            queryBuilder.where().eq("type", type);
            queryBuilder.orderBy("time", false); // false = descending order
            historyTableList = queryBuilder.query();
        } catch (SQLException throwables) {
            throwables.printStackTrace();
            historyTableList = new ArrayList<>();
        }
        return historyTableList;
    }

    public List<ResultHistoryTableBean> queryAllHistory() {
        List<ResultHistoryTableBean> historyTableList = null;
        try {
            // Query all with proper ordering (latest first)
            QueryBuilder<ResultHistoryTableBean, Integer> queryBuilder = dao.queryBuilder();
            queryBuilder.orderBy("time", false); // false = descending order
            historyTableList = queryBuilder.query();
        } catch (SQLException throwables) {
            throwables.printStackTrace();
            historyTableList = new ArrayList<>();
        }
        return historyTableList;
    }

}
