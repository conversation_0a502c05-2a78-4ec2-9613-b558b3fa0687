package com.dicernszh.botany

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.dicernszh.botany.base.BaseActivity
import com.dicernszh.botany.databinding.ActivityStreamResultBinding
import com.dicernszh.botany.v2.util.ToastUtil
import com.dicernszh.botany.qw.DetectAnything
import com.dicernszh.botany.qw.OnDetect
import com.dicernszh.botany.model.db.ResultHistoryDao
import com.dicernszh.botany.model.db.ResultHistoryTableBean
import OcrResponse
import io.noties.markwon.Markwon
import io.noties.markwon.html.HtmlPlugin
import io.noties.markwon.image.glide.GlideImagesPlugin
import rxhttp.wrapper.utils.GsonUtil
import java.io.File

/**
 * 支持流式输出的识别结果Activity
 */
class StreamResultActivity : BaseActivity() {
    companion object {
        const val RESULT_TYPE = "result_type"
        const val RESULT_PATH = "result_path"
        const val RESULT_DATA = "result_data" // 用于传递已有的识别结果
        const val IS_FROM_HISTORY = "is_from_history" // 标识是否来自历史记录
    }

    private lateinit var binding: ActivityStreamResultBinding
    private var detectAnything: DetectAnything? = null
    private var completeText = StringBuilder()
    private var markwon: Markwon? = null
    private var resultHistoryDao: ResultHistoryDao? = null
    private var currentResultType: Int = 0
    private var currentImagePath: String? = null
    private var isFromHistory: Boolean = false
    private var historyResultData: String? = null

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityStreamResultBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化历史记录DAO
        resultHistoryDao = ResultHistoryDao(this)

        // 获取参数
        currentResultType = intent.getIntExtra(RESULT_TYPE, 0)
        currentImagePath = intent.getStringExtra(RESULT_PATH)
        isFromHistory = intent.getBooleanExtra(IS_FROM_HISTORY, false)
        historyResultData = intent.getStringExtra(RESULT_DATA)

        // 初始化Markdown渲染器
        initMarkwon()

        // 设置顶部栏
        setupTopBar()

        // 设置按钮点击事件
        setupButtons()

        // 初始化界面状态
        initUI()

        // 开始识别或显示历史结果
        if (isFromHistory) {
            showHistoryResult()
        } else {
            startRecognitionProcess()
        }
    }

    private fun initMarkwon() {
        try {
            Log.d("StreamResult", "开始初始化Markwon...")
            // 使用最基本的Markwon配置
            markwon = Markwon.create(this)
            Log.d("StreamResult", "Markwon初始化成功")

        } catch (e: Exception) {
            Log.e("StreamResult", "Markwon初始化失败", e)
            e.printStackTrace()
            // 如果还是失败，则不使用Markwon
            markwon = null
        }
    }

    private fun setupTopBar() {
        binding.topBar.setTitle(getString(R.string.recognition_result)).apply {
            setTextColor(Color.parseColor("#000000"))
        }
        binding.topBar.addLeftBackImageButton().apply {
            setImageDrawable(
                ContextCompat.getDrawable(
                    this@StreamResultActivity,
                    R.drawable.ic_black_nav_back
                )
            )
            setOnClickListener {
                detectAnything?.cancel()
                finish()
            }
        }
    }

    private fun setupButtons() {
        binding.btnCopy.setOnClickListener {
            setClipData()
        }

        binding.btnShare.setOnClickListener {
            shareResult()
        }
    }

    private fun initUI() {
        if (isFromHistory) {
            // 来自历史记录，不显示识别状态
            binding.tvStatus.text = getString(R.string.recognition_result)
            binding.progressBar.visibility = View.GONE
        } else {
            // 新识别，显示识别状态
            binding.tvStatus.text = getString(R.string.recognizing)
            binding.progressBar.visibility = View.VISIBLE
        }

        // 测试Markdown渲染
//        testMarkdownRendering()
    }

    private fun showHistoryResult() {
        try {
            if (!currentImagePath.isNullOrEmpty() && File(currentImagePath!!).exists()) {
                loadImageWithCircularCrop(currentImagePath!!)
            }

            if (!historyResultData.isNullOrEmpty()) {
                // 尝试解析为OcrResponse
                try {
                    val ocrResponse = GsonUtil.fromJson(historyResultData, OcrResponse::class.java)
                    if (ocrResponse?.choices != null && ocrResponse.choices.isNotEmpty()) {
                        val content = ocrResponse.choices[0].message?.content
                        if (!content.isNullOrEmpty()) {
                            completeText.append(content)
                            renderMarkdown(content)
                            binding.tvStatus.text = "历史记录"
                            return
                        }
                    }
                } catch (e: Exception) {
                    Log.d("StreamResult", "不是OcrResponse格式，尝试直接显示文本")
                }

                // 如果不是OcrResponse格式，直接显示文本
                completeText.append(historyResultData!!)
                renderMarkdown(historyResultData!!)
                binding.tvStatus.text = "历史记录"
            } else {
                showError("历史记录数据为空")
            }
        } catch (e: Exception) {
            Log.e("StreamResult", "显示历史记录失败", e)
            showError("显示历史记录失败")
        }
    }

//    private fun testMarkdownRendering() {
//        // 先测试最简单的Markdown
//        val simpleMarkdown = "# 测试标题\n\n**这是粗体文本**\n\n- 列表项1\n- 列表项2"
//
//        Log.d("StreamResult", "开始测试Markdown渲染")
//        Log.d("StreamResult", "Markwon对象: $markwon")
//        Log.d("StreamResult", "TextView: ${binding.tvResult}")
//
//        renderMarkdown(simpleMarkdown)
//    }

    private fun renderMarkdown(content: String) {
        try {
            if (markwon != null) {
                Log.d("StreamResult", "使用Markwon渲染内容，长度: ${content.length}")
                markwon!!.setMarkdown(binding.tvResult, content)
                Log.d("StreamResult", "Markwon渲染完成")
            } else {
                Log.d("StreamResult", "Markwon为null，使用普通文本显示")
                binding.tvResult.text = content
            }
        } catch (e: Exception) {
            Log.e("StreamResult", "Markwon渲染失败", e)
            e.printStackTrace()
            binding.tvResult.text = content
        }
    }

    private fun startRecognitionProcess() {
        try {
            val resultType = intent.getIntExtra(RESULT_TYPE, 0)
            val resultPath = intent.getStringExtra(RESULT_PATH)

            if (resultPath != null && File(resultPath).exists()) {
                loadImageWithCircularCrop(resultPath)
                startRecognition(resultPath, resultType)
            } else {
                showError(getString(R.string.image_not_found))
            }
        } catch (e: Exception) {
            showError(getString(R.string.recognition_error))
        }
    }

    private fun loadImageWithCircularCrop(imagePath: String) {
        Glide.with(this)
            .load(File(imagePath))
            .apply(RequestOptions.circleCropTransform())
            .into(binding.ivResult)
    }

    private fun showError(message: String) {
        binding.progressBar.visibility = View.GONE
        binding.tvStatus.text = message
        renderMarkdown("## ❌ 错误\n\n$message")
    }

    private fun startRecognition(filePath: String, resultType: Int) {
        detectAnything = DetectAnything(this, filePath, resultType)

        detectAnything?.start(object : OnDetect<OcrResponse> {
            override fun onSuccess(data: OcrResponse?) {
                runOnUiThread {
                    // 识别完成
                    binding.progressBar.visibility = View.GONE
                    binding.tvStatus.text = "识别完成"

                    // 如果没有通过流式输出获得内容，使用最终结果
                    if (completeText.isEmpty() && data?.choices != null && data.choices.isNotEmpty()) {
                        val content = data.choices[0].message?.content
                        if (!content.isNullOrEmpty()) {
                            completeText.append(content)
                            renderMarkdown(content)
                            autoScrollToBottom()
                        }
                    }

                    // 保存历史记录（只有新识别才保存）
                    if (!isFromHistory) {
                        saveToHistory(data)
                    }
                }
            }

            override fun onFail(msg: String?) {
                runOnUiThread {
                    val errorMessage = msg ?: "未知错误"
                    showError(getString(R.string.recognition_failed, errorMessage))
                }
            }

            override fun onStreamData(data: String) {
                runOnUiThread {
                    // 流式输出：将新数据追加到现有文本
                    completeText.append(data)

                    // 使用Markdown渲染完整文本
                    renderMarkdown(completeText.toString())

                    // 自动滚动到底部
                    autoScrollToBottom()
                }
            }
        })
    }

    private fun autoScrollToBottom() {
        binding.scrollView.post {
            binding.scrollView.fullScroll(View.FOCUS_DOWN)
        }
    }

    private fun saveToHistory(data: OcrResponse?) {
        try {
            if (completeText.isNotEmpty() && !currentImagePath.isNullOrEmpty() && resultHistoryDao != null) {
                val historyBean = ResultHistoryTableBean().apply {
                    localPath = currentImagePath!!
                    title = getResultTypeTitle(currentResultType)
                    type = currentResultType
                    result = if (data != null) GsonUtil.toJson(data) else completeText.toString()
                    time = System.currentTimeMillis()
                }
                resultHistoryDao!!.insertHistoryItem(historyBean)
            }
        } catch (e: Exception) {
            Log.e("StreamResult", "保存历史记录失败", e)
        }
    }

    private fun getResultTypeTitle(type: Int): String {
        return when (type) {
            Image2TxtResultAct.RESULT_TYPE_PLANT -> getString(R.string.plant_recognition)
            Image2TxtResultAct.RESULT_TYPE_ANIMAL -> getString(R.string.animal_recognition)
            Image2TxtResultAct.RESULT_TYPE_FLOWER -> getString(R.string.flower_recognition)
            Image2TxtResultAct.RESULT_TYPE_FRUIT -> getString(R.string.fruit_veg_recognition)
            Image2TxtResultAct.RESULT_TYPE_LOGO -> getString(R.string.logo_recognition)
            Image2TxtResultAct.RESULT_TYPE_DISH -> getString(R.string.dish_recognition)
            Image2TxtResultAct.RESULT_TYPE_LAND -> getString(R.string.landmark_recognition)
            Image2TxtResultAct.RESULT_TYPE_MMONEY -> getString(R.string.currency_recognition)
            Image2TxtResultAct.RESULT_TYPE_GOOD -> getString(R.string.product_recognition)
            Image2TxtResultAct.RESULT_TYPE_NET_WORD -> getString(R.string.text_recognition)
            else -> "识别结果"
        }
    }

    private fun setClipData() {
        if (completeText.isNotEmpty()) {
            com.dicernszh.botany.ad.AdSdk.getInstance().showInterAd(this)
            val cm: ClipboardManager = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
            val mClipData: ClipData = ClipData.newPlainText("识别结果", completeText.toString())
            cm.setPrimaryClip(mClipData)
            ToastUtil.showToast(this, getString(R.string.copy_success))
        } else {
            ToastUtil.showToast(this, "暂无内容可复制")
        }
    }

    private fun shareResult() {
        if (completeText.isNotEmpty()) {
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, completeText.toString())
                putExtra(Intent.EXTRA_SUBJECT, "识别结果分享")
            }
            startActivity(Intent.createChooser(shareIntent, getString(R.string.share)))
        } else {
            ToastUtil.showToast(this, "暂无内容可分享")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        detectAnything?.cancel()
    }
}
