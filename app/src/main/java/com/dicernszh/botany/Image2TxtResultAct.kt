package com.dicernszh.botany

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.View
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.dicernszh.botany.model.*
import com.dicernszh.botany.ad.AdSdk
import com.dicernszh.botany.base.BaseActivity
import com.dicernszh.botany.databinding.ActResultLayoutBinding
import com.dicernszh.botany.v2.util.ToastUtil
import OcrResponse
import java.io.File


class Image2TxtResultAct : BaseActivity() {
    companion object {
        const val RESULT_TYPE = "result_type"
        const val RESULT_DATA = "result_data"
        const val RESULT_PATH = "result_path"
        const val RESULT_TYPE_GENERIC = 0
        const val RESULT_TYPE_BANK = 1
        const val RESULT_TYPE_ID = 2
        const val RESULT_TYPE_NET_WORD = 3
        const val RESULT_TYPE_QR = 4
        const val RESULT_TYPE_HAND_WRITE = 5
        const val RESULT_TYPE_STUDENT = 6
        const val RESULT_TYPE_LICENCE = 7
        const val RESULT_TYPE_PLANT = 8
        const val RESULT_TYPE_ANIMAL = 9
        const val RESULT_TYPE_FRUIT = 10
        const val RESULT_TYPE_FORM = 11
        const val RESULT_TYPE_FLOWER = 12
        const val RESULT_TYPE_LOGO = 13
        const val RESULT_TYPE_DISH = 14
        const val RESULT_TYPE_LAND = 15
        const val RESULT_TYPE_MMONEY = 16
        const val RESULT_TYPE_GOOD = 17

        // 新增AI接口的结果类型，与DetectAnything.kt中的类型对应
        const val RESULT_TYPE_GENERIC_ALBUM = 0
        const val RESULT_TYPE_GENERIC_SHOT = 1

        // OCR相关类型（>= 10）
        const val RESULT_TYPE_OCR = 10001
    }

    private lateinit var binding: ActResultLayoutBinding

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActResultLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        binding.topBar.setTitle(getString(R.string.recognition_result)).apply {
            setTextColor(Color.parseColor("#000000"))
        }
        binding.topBar.addLeftBackImageButton().apply {
            setImageDrawable(
                ContextCompat.getDrawable(
                    this@Image2TxtResultAct,
                    R.drawable.ic_black_nav_back
                )
            )
            setOnClickListener {
                finish()
            }
        }
        binding.btnCopy.setOnClickListener {
            setClipData()
        }
        try {
            var resultType = intent.getIntExtra(RESULT_TYPE, 0)
            var resultPath = intent.getStringExtra(RESULT_PATH)
            if (resultPath != null && File(resultPath).exists()) {
                Glide.with(this).load(File(resultPath)).into(binding.ivResult)
            }
            when (resultType) {
                // 新AI接口的结果处理
                RESULT_TYPE_GENERIC_ALBUM, RESULT_TYPE_GENERIC_SHOT,
                RESULT_TYPE_ANIMAL, RESULT_TYPE_PLANT, RESULT_TYPE_FLOWER,
                RESULT_TYPE_FRUIT, RESULT_TYPE_DISH, RESULT_TYPE_LOGO,
                RESULT_TYPE_LAND, RESULT_TYPE_MMONEY, RESULT_TYPE_OCR -> {
                    var resultData = intent.getParcelableExtra<OcrResponse>(RESULT_DATA)
                    if (resultData?.choices != null && resultData.choices.isNotEmpty()) {
                        val content = resultData.choices[0].message?.content
                        if (!content.isNullOrEmpty()) {
                            binding.tvResult.gravity = Gravity.START
                            binding.tvResult.text = content
                        } else {
                            binding.tvResult.text = getString(R.string.no_recognition_result)
                        }
                    } else {
                        binding.tvResult.text = getString(R.string.no_recognition_result)
                    }
                }

                // 保持对旧接口的兼容性
                RESULT_TYPE_BANK -> {
                    var resultData = intent.getParcelableExtra<DetectBankData>(RESULT_DATA)
                    if (resultData?.result != null) {
                        binding.tvResult.text =
                            getString(R.string.bank_info_format,
                                resultData?.result?.bank_name,
                                resultData?.result?.bank_card_number)
                    }
                }
                RESULT_TYPE_ID -> {
                    var resultData = intent.getParcelableExtra<DetectIDData>(RESULT_DATA)
                    if (resultData?.words_result != null) {
                        var wdResult = resultData?.words_result
                        binding.tvResult.gravity = Gravity.START
                        binding.tvResult.text =
                            getString(R.string.id_info_format,
                                wdResult?.姓名?.words,
                                wdResult?.公民身份号码?.words,
                                wdResult?.性别?.words,
                                wdResult?.出生?.words,
                                wdResult?.民族?.words,
                                wdResult?.住址?.words)
                    }
                }
                RESULT_TYPE_LICENCE -> {
                    var resultData = intent.getParcelableExtra<DetectDriverData>(RESULT_DATA)
                    binding.tvResult.text = getString(R.string.license_info_format,
                        resultData!!.words_result.住址?.words,
                        resultData.words_result.准驾车型?.words,
                        resultData.words_result.至?.words,
                        resultData.words_result.出生日期?.words,
                        resultData.words_result.证号?.words,
                        resultData.words_result.住址?.words,
                        resultData.words_result.初次领证日期?.words,
                        resultData.words_result.国籍?.words,
                        resultData.words_result.准驾车型?.words,
                        resultData.words_result.性别?.words,
                        resultData.words_result.有效期限?.words)
                }
                RESULT_TYPE_NET_WORD -> {
                    var resultData = intent.getParcelableExtra<DetectWordData>(RESULT_DATA)
                    if (resultData?.words_result != null) {
                        var wdResult = resultData?.words_result
                        var resultStr = ""
                        wdResult?.forEach {
                            resultStr += "${it.words}\n"
                        }
                        binding.tvResult.text = resultStr
                    }
                }

                RESULT_TYPE_QR -> {
                    var resultData = intent.getParcelableExtra<DetectQRData>(RESULT_DATA)
                    var resultStr = "";
                    resultData?.codes_result?.forEach {
                        resultStr += ("${it.type}\n${it.text?.joinToString()}\n\n")
                    }
                    binding.tvResult.text = resultStr
                }
                RESULT_TYPE_HAND_WRITE -> {
                    var resultData = intent.getParcelableExtra<DetectWordData>(RESULT_DATA)
                    var result = ""
                    if (resultData != null && resultData.words_result_num!! > 0) {
                        resultData.words_result?.forEach {
                            result += "${it.words}\n"
                        }
                    }
                    binding.tvResult.text = result
                }
                RESULT_TYPE_STUDENT -> {
                    var resultData = intent.getParcelableExtra<DetectWordData>(RESULT_DATA)
                    var result = ""
                    if (resultData != null && resultData.words_result_num!! > 0) {
                        resultData.words_result?.forEach {
                            result += "${it.words}\n"
                        }
                    }
                    binding.tvResult.text = result
                }
            }
        } catch (e: Exception) {
            // Log exception or handle appropriately
        }
    }

    private fun setClipData() {
        com.dicernszh.botany.ad.AdSdk.getInstance().showInterAd(this)
        var cm: ClipboardManager = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
        var mClipData: ClipData = ClipData.newPlainText("Label", binding.tvResult.text.toString())
        cm.setPrimaryClip(mClipData)
        ToastUtil.showToast(this, getString(R.string.copy_success))
    }




}