package com.dicernszh.botany

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.*
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.dicernszh.botany.CameraActivity.DATA_KEY_DATA
import com.dicernszh.botany.CameraActivity.DATA_KEY_TYPE
import com.dicernszh.botany.Image2ObjResultAct.Companion.RESULT_TYPE_GENERIC
import com.dicernszh.botany.Image2ObjResultAct.Companion.RESULT_TYPE_GENERIC_LOCAL
import com.dicernszh.botany.databinding.FragmentHomeLayoutBinding
import com.dicernszh.botany.model.ImageDetectData
import com.dicernszh.botany.model.db.ResultHistoryDao
import com.dicernszh.botany.model.db.ResultHistoryTableBean
import com.dicernszh.botany.net.DetectObjBack
import com.dicernszh.botany.net.DetectObjTask
import com.dicernszh.botany.net.Url
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.qmuiteam.qmui.widget.dialog.QMUIDialog.MessageDialogBuilder
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction
import rxhttp.wrapper.utils.GsonUtil

class HomeFragment : Fragment() {
    var resultDao: ResultHistoryDao? = null

    private lateinit var storagePermissionLauncher: ActivityResultLauncher<String>
    private lateinit var mediaImagesPermissionLauncher: ActivityResultLauncher<Array<String>>
    
    // 添加ViewBinding
    private var _binding: FragmentHomeLayoutBinding? = null
    private val binding get() = _binding!!

    private fun isStoragePermissionGranted(): Boolean {
        return if (Build.VERSION.SDK_INT < 33) {
            PermissionUtils.isGranted(Manifest.permission.READ_EXTERNAL_STORAGE)
        } else {
            PermissionUtils.isGranted(Manifest.permission.READ_MEDIA_IMAGES)
        }
    }

    private val requestCameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            currentCameraCallback?.invoke()
        } else {
            ToastUtils.showLong(getString(R.string.permission_camera_denied))
        }
    }

    // Store callback temporarily
    private var currentCameraCallback: (() -> Unit)? = null

    // 存储当前的识别类型
    private var currentRecognitionType: Int = Image2TxtResultAct.RESULT_TYPE_GENERIC_SHOT

    // 系统相册选择器
    private val albumLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                val filePath = getRealPathFromURI(uri)
                if (filePath != null) {
                    scanLocal(filePath, currentRecognitionType)
                } else {
                    // 如果无法获取真实路径，尝试使用URI直接处理
                    handleImageUri(uri)
                }
            }
        }
    }

    // 系统相机拍照
    private var photoFile: File? = null
    private val cameraLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && photoFile != null) {
            scanLocal(photoFile!!.absolutePath, currentRecognitionType)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        resultDao = ResultHistoryDao(activity)
        initPermissionLaunchers()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeLayoutBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun scanLocal(filePath: String?, resultType: Int = Image2TxtResultAct.RESULT_TYPE_GENERIC_ALBUM) {
        if (filePath != null) {
            // 使用新的AI接口进行识别
            val intent = Intent(activity, com.dicernszh.botany.StreamResultActivity::class.java).apply {
                putExtra(com.dicernszh.botany.StreamResultActivity.RESULT_TYPE, resultType)
                putExtra(com.dicernszh.botany.StreamResultActivity.RESULT_PATH, filePath)
            }
            startActivity(intent)
        }
    }

    private fun openAlbum() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        intent.type = "image/*"
        albumLauncher.launch(intent)
    }

    // 从URI获取真实路径
    private fun getRealPathFromURI(uri: Uri): String? {
        var result: String? = null
        val cursor: Cursor? = activity?.contentResolver?.query(uri, null, null, null, null)
        if (cursor != null) {
            if (cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndex(MediaStore.Images.Media.DATA)
                if (columnIndex >= 0) {
                    result = cursor.getString(columnIndex)
                }
            }
            cursor.close()
        }
        return result
    }

    // 处理无法获取真实路径的URI
    private fun handleImageUri(uri: Uri) {
        try {
            val inputStream: InputStream? = activity?.contentResolver?.openInputStream(uri)
            if (inputStream != null) {
                // 创建临时文件
                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val tempFile = File(activity?.cacheDir, "temp_image_$timeStamp.jpg")

                val outputStream = FileOutputStream(tempFile)
                inputStream.copyTo(outputStream)
                inputStream.close()
                outputStream.close()

                scanLocal(tempFile.absolutePath, currentRecognitionType)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            ToastUtils.showLong("处理图片失败")
        }
    }

    // 创建系统相机拍照文件
    private fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = activity?.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile("JPEG_${timeStamp}_", ".jpg", storageDir)
    }

    // 使用系统相机拍照
    private fun takePhotoWithSystemCamera(recognitionType: Int = Image2TxtResultAct.RESULT_TYPE_GENERIC_SHOT) {
        try {
            currentRecognitionType = recognitionType
            photoFile = createImageFile()
            val photoURI = FileProvider.getUriForFile(
                requireContext(),
                "${requireContext().packageName}.fileprovider",
                photoFile!!
            )
            cameraLauncher.launch(photoURI)
        } catch (e: Exception) {
            e.printStackTrace()
            ToastUtils.showLong("启动相机失败")
        }
    }

    // 选择相册图片
    private fun selectFromAlbum(recognitionType: Int = Image2TxtResultAct.RESULT_TYPE_GENERIC_ALBUM) {
        currentRecognitionType = recognitionType
        chooseImage()
    }

    private fun initPermissionLaunchers() {
        storagePermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                openAlbum()
            } else {
                ToastUtils.showLong(getString(R.string.permission_storage_denied))
            }
        }

        mediaImagesPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            val allGranted = permissions.entries.all { it.value }
            if (allGranted) {
                openAlbum()
            } else {
                ToastUtils.showLong(getString(R.string.permission_storage_denied))
            }
        }
    }

    private fun chooseImage() {
        if (isStoragePermissionGranted()) {
            openAlbum()
        } else {
            val permsTips = if (Build.VERSION.SDK_INT < 33) {
                getString(R.string.permission_storage_rationale)
            } else {
                getString(R.string.permission_media_rationale)
            }

            MessageDialogBuilder(activity).setMessage(permsTips).addAction(
                QMUIDialogAction( getString(R.string.cancel)
                ) { dialog, index -> dialog.dismiss() }
            ).addAction(
                QMUIDialogAction( getString(R.string.allow)
                ) { dialog, index ->
                    dialog.dismiss()
                    applyStoragePermissions()
                }
            ).create().show()
        }
    }

    private fun applyStoragePermissions() {
        if (Build.VERSION.SDK_INT < 33) {
            storagePermissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
        } else {
            mediaImagesPermissionLauncher.launch(
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO
                )
            )
        }
    }

    private fun openCamera(callback: () -> Unit) {
        if (PermissionUtils.isGranted(Manifest.permission.CAMERA)) {
            callback()
        } else {
            MessageDialogBuilder(activity).setMessage(getString(R.string.permission_camera_denied))
                .addAction(
                    QMUIDialogAction( getString(R.string.cancel)
                    ) { dialog, index -> dialog.dismiss() }
                ).addAction(
                    QMUIDialogAction( getString(R.string.confirm)
                    ) { dialog, index ->
                        dialog.dismiss()
                        val reject = SPUtils.getInstance().getBoolean("reject")
                        if (!reject) {
                            currentCameraCallback = callback
                            requestCameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                        } else {
                            AppUtils.launchAppDetailsSettings()
                        }
                    }
                ).create().show()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        com.dicernszh.botany.ad.AdSdk.getInstance().showBanner(activity, binding.adContainer)
        
        binding.ivLocal.setOnClickListener {
            selectFromAlbum()
        }

        binding.ivScan.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera()
            }
        }

        binding.ivCell1.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_PLANT)
            }
        }
        
        binding.ivCell2.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_ANIMAL)
            }
        }

        binding.ivCell3.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_FLOWER)
            }
        }

        binding.ivCell4.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_FRUIT)
            }
        }
        
        binding.ivCell5.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_LOGO)
            }
        }

        binding.ivCell6.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_DISH)
            }
        }

        binding.ivCell7.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_LAND)
            }
        }

        binding.ivCell8.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_MMONEY)
            }
        }

        binding.ivCell9.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_GOOD)
            }
        }

        binding.ivCell10.setOnClickListener {
            openCamera {
                takePhotoWithSystemCamera(Image2TxtResultAct.RESULT_TYPE_OCR)
            }
        }
    }
}