<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="permission_request_denied">Permission request denied</string>
    <string name="error_gif">x or y bound size should be at least %1$dpx and file size should be no more than %2$sM</string>
    <string name="menu_scan_image">Scan</string>
    <string name="menu_history">History</string>
    <string name="menu_mine">My Profile</string>
    <string name="tab_home">Home</string>
    <string name="tab_fav">Tips</string>
    <string name="tab_mine">My Profile</string>
    <string name="tab_shop">Printing</string>
    <string name="tab_news">News</string>
    <string name="tab_scan_pic">Scan Photo</string>
    <string name="tab_scan_word">Precision</string>
    <string name="history" tools:ignore="ExtraTranslation">History</string>
    <string name="about_us">About Us</string>
    <string name="check_update">Check for Updates</string>
    <string name="feedback">Feedback</string>
    <string name="clear_cache">Clear Cache</string>
    <string name="private_policy">Privacy Policy</string>

    <string name="last_version">Already the latest version</string>
    <string name="no_market">No app store installed</string>
    <string name="clear_history">Cache cleared</string>
    <string name="qq">Contact us: <EMAIL></string>
    <string name="version">Current version: v</string>
    <string name="str_feedback_content">Please provide your valuable feedback</string>
    <string name="str_feedback_qq">Contact information (Facebook)</string>
    <string name="submit_tips">Your feedback content or contact information is empty</string>
    <string name="submit_success">Submitted successfully</string>
    <string name="submit">Submit</string>

    <string name="take_photo_skills">Photography Tips</string>
    <string name="tabSegment_item_1_title">Visa Photo Specifications</string>
    <string name="tabSegment_item_2_title">ID Photo Specifications</string>
    <string name="tabSegment_pre_1_title">Digital Photo</string>
    <string name="tabSegment_pre_2_title">Layout Photo</string>
    <string name="app_slogn">Smart Photography with Precise Image Recognition</string>
    <string name="again_info">We strictly protect users\' privacy information. If you do not agree to the policy, some functions of our app may be limited.</string>
    <string name="share_other">Recommend to Others</string>
    <string name="good_rate">Rate Us</string>
    <string name="content">This application respects and protects the personal privacy of all users. To provide you with more precise and personalized services, this application will use and disclose your personal information in accordance with the privacy policy.</string>


    <!-- General -->
    <string name="app_name">Plant Recognition</string>

    <!-- Top Bar -->
    <string name="home_title">Home</string>

    <!-- Photo Recognition Section -->
    <string name="take_photo_title">Camera Recognition</string>
    <string name="take_photo_desc">One-click photo recognition</string>

    <!-- Gallery Photo Recognition Section -->
    <string name="gallery_photo_title">Gallery Recognition</string>
    <string name="gallery_photo_desc">Recognize local images freely</string>

    <!-- Precise Recognition Title -->
    <string name="precise_recognition">Precise Recognition</string>

    <!-- Plant Recognition -->
    <string name="plant_recognition">Plant Recognition</string>
    <string name="plant_recognition_desc">Over 20,000 plant species</string>

    <!-- Animal Recognition -->
    <string name="animal_recognition">Animal Recognition</string>
    <string name="animal_recognition_desc">Over 8,000 animal species</string>

    <!-- Flower Recognition -->
    <string name="flower_recognition">Flower Recognition</string>
    <string name="flower_recognition_desc">Over 10,000 flower types</string>

    <!-- Fruit & Vegetable Recognition -->
    <string name="fruit_veg_recognition">Fruit &amp; Vegetable</string>
    <string name="fruit_veg_recognition_desc">Thousands of fruits and vegetables</string>

    <!-- Brand Logo Recognition -->
    <string name="logo_recognition">Brand Logo Recognition</string>
    <string name="logo_recognition_desc">Over 10,000 brand logos</string>

    <!-- Dish Recognition -->
    <string name="dish_recognition">Dish Recognition</string>
    <string name="dish_recognition_desc">Nearly 1,000 dishes</string>

    <!-- Landmark Recognition -->
    <string name="landmark_recognition">Landmark Recognition</string>
    <string name="landmark_recognition_desc">World-famous landmarks</string>

    <!-- Currency Recognition -->
    <string name="currency_recognition">Currency Recognition</string>
    <string name="currency_recognition_desc">Global currencies</string>

    <!-- Product Recognition -->
    <string name="product_recognition">Product Recognition</string>
    <string name="product_recognition_desc">Thousands of products</string>

    <!-- Text Recognition -->
    <string name="text_recognition">Text Recognition</string>
    <string name="text_recognition_desc">Accurate text extraction</string>
    <string name="history_empty_text">No history records</string>
    <string name="recognition_result">Recognition Result</string>
    <string name="choose_browser">Please select a browser</string>
    <string name="baidu_url">https://simple.m.wikipedia.org/wiki/%s</string>
    <string name="possible_recognition_result">The recognition result might be:</string>
    <string name="encyclopedia">Encyclopedia</string>

    <!-- New strings for Image2TxtResultAct -->
    <string name="copy_success">Copy successful!</string>

    <!-- New strings for StreamResultActivity -->
    <string name="recognizing">Recognizing...</string>
    <string name="image_not_found">Image not found</string>
    <string name="recognition_error">Recognition error occurred</string>
    <string name="recognition_failed">Recognition failed: %s</string>
    <string name="no_recognition_result">No recognition result</string>
    <string name="copy">Copy</string>
    <string name="share">Share</string>

    <!-- Format strings for displaying recognition results -->
    <string name="bank_info_format">Bank: %1$s\nBank Card Number: %2$s</string>

    <string name="id_info_format">Name: %1$s\nID Number: %2$s\nGender: %3$s\nDate of Birth: %4$s\nEthnicity: %5$s\nAddress: %6$s</string>

    <string name="license_info_format">Address: %1$s\nPermitted Vehicle Types: %2$s\nValid Until: %3$s\nDate of Birth: %4$s\nLicense Number: %5$s\nAddress: %6$s\nFirst Issue Date: %7$s\nNationality: %8$s\nPermitted Vehicle Types: %9$s\nGender: %10$s\nValidity Period: %11$s</string>

    <!-- Album text -->
    <string name="album">Album</string>
    <!-- Flash text -->
    <string name="flash_on">Flash on</string>
    <string name="flash_off">Flash Off</string>


    <!-- Permission related messages -->
    <string name="permission_storage_denied">Please allow storage permission</string>
    <string name="permission_storage_rationale">Storage permission is required to select images. Allow?</string>
    <string name="permission_media_rationale">Media permission is required to access your photos. Allow?</string>
    <string name="permission_camera_denied">Please enable camera permission!</string>

    <!-- Dialog buttons -->
    <string name="cancel">Cancel</string>
    <string name="allow">Allow</string>
    <string name="confirm">Confirm</string>

    <!-- Exit Dialog -->
    <string name="exit_dialog_title">Notice</string>
    <string name="exit_dialog_message">Are you sure you want to exit?</string>
    <string name="exit_dialog_positive">Yes</string>

    <string name="share_app_message">Download it now from the app store!</string>
    <string name="share_app_title">Share App</string>
    <string name="cannot_open_store">Cannot open app store</string>

    <string name="service_agreement">Service Agreement</string>
    <string name="privacy_policy_title">Privacy Policy</string>
    <string name="read_more">Read</string>
    <string name="and">and</string>
    <string name="period">.</string>
    <string name="btn_cancel">Cancel</string>
    <string name="btn_agree">Agree</string>

    <string name="welcome_app">Welcome to APP</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="btn_agree_and_continue">Agree</string>
    <string name="btn_disagree_and_exit">Disagree and Exit</string>

    <!-- AI Recognition Prompts -->
    <!-- OCR Document Recognition -->
    <string name="prompt_ocr_doc">Please recognize all text content in the image and output in Markdown format, strictly maintaining the original layout, format and structure.\n\n## Recognition Requirements\n1. **Accurate extraction**: All visible text, including punctuation and special characters\n2. **Preserve structure**: Original text paragraphs, indentation, lists and table structure\n3. **Reading order**: Multi-column text recognized from left to right, top to bottom\n4. **Chart text**: Include text in charts or graphics\n5. **Empty content handling**: If no recognizable content, return \"## Recognition Result\\n\\nNo content recognized\"\n6. **Blur annotation**: For unclear text, mark as `[Blurred]`\n\nPlease output directly in Markdown format, starting with \"# Text Recognition Result\", followed by the recognized text content.</string>

    <!-- Generic Recognition -->
    <string name="prompt_generic">Please recognize the main content in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify the main objects, scenes or content in the image\n- Provide detailed descriptions and related information\n- If it\'s an item, explain its uses, features, etc.\n- If it\'s a scene, describe the environment, atmosphere, etc.\n\nPlease output directly in Markdown format, starting with \"# Image Recognition Result\", including main content, detailed description and related information sections.</string>

    <!-- Animal Recognition -->
    <string name="prompt_animal">Please recognize the animal in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify animal species\n- Provide scientific classification information\n- Describe morphological features and living habits\n- Include important information such as conservation status\n\nPlease output directly in Markdown format, starting with \"# 🐾 Animal Recognition Result\", including basic information, taxonomic information, morphological features, habitat, living habits, distribution area, reproduction methods, conservation status and cultural significance sections.</string>

    <!-- Plant Recognition -->
    <string name="prompt_plant">Please recognize the plant in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify plant species\n- Provide scientific classification and morphological features\n- Include cultivation and usage information\n- Describe ecological value and cultural significance\n\nPlease output directly in Markdown format, starting with \"# 🌿 Plant Recognition Result\", including basic information, taxonomic information, morphological features, growth environment, distribution range, cultivation techniques, usage value and cultural significance sections.</string>

    <!-- Fruit Recognition -->
    <string name="prompt_fruit">Please recognize the fruit in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify fruit species and varieties\n- Provide nutritional composition and calorie information\n- Include purchasing and storage recommendations\n- Describe taste and consumption methods\n\nPlease output directly in Markdown format, starting with \"# 🍎 Fruit Recognition Result\", including basic information, nutritional information, quality characteristics, purchasing recommendations, storage methods, consumption recommendations and health benefits sections.</string>

    <!-- Dish Recognition -->
    <string name="prompt_dish">Please recognize the dish in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify dish name and type\n- Provide nutritional and calorie information\n- Include preparation methods and ingredients\n- Describe cultural background and characteristics\n\nPlease output directly in Markdown format, starting with \"# 🍽️ Dish Recognition Result\", including basic information, nutritional information, taste characteristics, main ingredients, preparation methods, cultural background, suitable groups and nutritional value sections.</string>

    <!-- Logo Recognition -->
    <string name="prompt_logo">Please recognize the brand logo in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify brand name and logo\n- Provide company and industry information\n- Describe design features and meanings\n- Include brand history and influence\n\nPlease output directly in Markdown format, starting with \"# 🏷️ Brand Logo Recognition Result\", including brand basic information, company information, logo design analysis, brand information, market influence, development history and social responsibility sections.</string>

    <!-- Landmark Recognition -->
    <string name="prompt_landmark">Please recognize the landmark building in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify landmark building name\n- Provide historical and architectural information\n- Describe cultural and tourism value\n- Include practical visiting information\n\nPlease output directly in Markdown format, starting with \"# 🏛️ Landmark Building Recognition Result\", including basic information, architectural information, historical background, architectural features, functional uses, cultural value, protection status, tourism information and interesting facts sections.</string>

    <!-- Currency Recognition -->
    <string name="prompt_currency">Please recognize the currency in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify currency type and denomination\n- Provide issuing country and year information\n- Describe design patterns and security features\n- Include historical and cultural background\n\nPlease output directly in Markdown format, starting with \"# 💰 Currency Recognition Result\", including basic information, issuance information, design features, security features, historical culture, economic information and interesting facts sections.</string>

    <!-- Default Recognition -->
    <string name="prompt_default">Please recognize the main content in the image and output detailed information in Markdown format.\n\n## Recognition Requirements\n- Accurately identify the main objects or content in the image\n- Provide detailed descriptions and related information\n- Include practical background knowledge\n- Ensure information accuracy and reliability\n\nPlease output directly in Markdown format, starting with \"# 🔍 Image Recognition Result\", including recognized content, detailed information, feature description and related knowledge sections.</string>

    <!-- Error Messages -->
    <string name="error_image_empty">Image cannot be empty</string>
    <string name="error_request_failed">Request failed: %s</string>
    <string name="error_http_error">HTTP error: %d</string>
    <string name="error_response_empty">Response body is empty</string>
    <string name="error_stream_abnormal_end">Response stream ended abnormally</string>
    <string name="error_read_stream_failed">Failed to read response stream: %s</string>

    <!-- UI Messages -->
    <string name="detecting_image">Detecting image</string>
</resources>