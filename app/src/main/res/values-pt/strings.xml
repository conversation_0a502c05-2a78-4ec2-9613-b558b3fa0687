<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="permission_request_denied">Solicitação de permissão negada</string>
    <string name="error_gif">O tamanho de limite x ou y deve ser pelo menos %1$dpx e o tamanho do arquivo não deve ser superior a %2$sM</string>
    <string name="menu_scan_image">Digitalizar</string>
    <string name="menu_history">Histórico</string>
    <string name="menu_mine">Meu Perfil</string>
    <string name="tab_home">In<PERSON>cio</string>
    <string name="tab_fav">Dicas</string>
    <string name="tab_mine">Meu Perfil</string>
    <string name="tab_shop">Impressão</string>
    <string name="tab_news">Notícias</string>
    <string name="tab_scan_pic">Digitalizar Foto</string>
    <string name="tab_scan_word">Precisão</string>
    <string name="history" tools:ignore="ExtraTranslation">Histórico</string>
    <string name="about_us">Sobre Nós</string>
    <string name="check_update">Verificar Atualizações</string>
    <string name="feedback">Feedback</string>
    <string name="clear_cache">Limpar Cache</string>
    <string name="private_policy">Política de Privacidade</string>

    <string name="last_version">Já está na versão mais recente</string>
    <string name="no_market">Nenhuma loja de aplicativos instalada</string>
    <string name="clear_history">Cache limpo</string>
    <string name="qq">Entre em contato: <EMAIL></string>
    <string name="version">Versão atual: v</string>
    <string name="str_feedback_content">Por favor, forneça seu valioso feedback</string>
    <string name="str_feedback_qq">Informações de contato (Facebook)</string>
    <string name="submit_tips">Seu conteúdo de feedback ou informações de contato estão vazios</string>
    <string name="submit_success">Enviado com sucesso</string>
    <string name="submit">Enviar</string>

    <string name="take_photo_skills">Dicas de Fotografia</string>
    <string name="tabSegment_item_1_title">Especificações de Foto de Visto</string>
    <string name="tabSegment_item_2_title">Especificações de Foto de Identificação</string>
    <string name="tabSegment_pre_1_title">Foto Digital</string>
    <string name="tabSegment_pre_2_title">Foto de Layout</string>
    <string name="app_slogn">Fotografia Inteligente com Reconhecimento Preciso de Imagem</string>
    <string name="again_info">Protegemos rigorosamente as informações de privacidade dos usuários. Se você não concordar com a política, algumas funções do nosso aplicativo podem ser limitadas.</string>
    <string name="share_other">Recomendar a Outros</string>
    <string name="good_rate">Avalie-nos</string>
    <string name="content">Este aplicativo respeita e protege a privacidade pessoal de todos os usuários. Para fornecer serviços mais precisos e personalizados, este aplicativo usará e divulgará suas informações pessoais de acordo com a política de privacidade.</string>

    <!-- General -->
    <string name="app_name">Reconhecimento de Plantas</string>

    <!-- Top Bar -->
    <string name="home_title">Início</string>

    <!-- Photo Recognition Section -->
    <string name="take_photo_title">Reconhecimento por Câmera</string>
    <string name="take_photo_desc">Reconhecimento com\num clique</string>

    <!-- Gallery Photo Recognition Section -->
    <string name="gallery_photo_title">Reconhecimento de Galeria</string>
    <string name="gallery_photo_desc">Reconheça imagens\nlocais livremente</string>

    <!-- Precise Recognition Title -->
    <string name="precise_recognition">Reconhecimento Preciso</string>

    <!-- Plant Recognition -->
    <string name="plant_recognition">Reconhecimento de Plantas</string>
    <string name="plant_recognition_desc">Mais de 20.000\nespécies de plantas</string>

    <!-- Animal Recognition -->
    <string name="animal_recognition">Reconhecimento de Animais</string>
    <string name="animal_recognition_desc">Mais de 8.000\nespécies de animais</string>

    <!-- Flower Recognition -->
    <string name="flower_recognition">Reconhecimento de Flores</string>
    <string name="flower_recognition_desc">Mais de 10.000\ntipos de flores</string>

    <!-- Fruit & Vegetable Recognition -->
    <string name="fruit_veg_recognition">Frutas e Vegetais</string>
    <string name="fruit_veg_recognition_desc">Milhares de frutas\ne vegetais</string>

    <!-- Brand Logo Recognition -->
    <string name="logo_recognition">Reconhecimento de Logo</string>
    <string name="logo_recognition_desc">Mais de 10.000\nlogos de marcas</string>

    <!-- Dish Recognition -->
    <string name="dish_recognition">Reconhecimento de Pratos</string>
    <string name="dish_recognition_desc">Quase 1.000 pratos</string>

    <!-- Landmark Recognition -->
    <string name="landmark_recognition">Reconhecimento de Pontos Turísticos</string>
    <string name="landmark_recognition_desc">Pontos turísticos\nmundialmente famosos</string>

    <!-- Currency Recognition -->
    <string name="currency_recognition">Reconhecimento de Moeda</string>
    <string name="currency_recognition_desc">Moedas globais</string>

    <!-- Product Recognition -->
    <string name="product_recognition">Reconhecimento de Produtos</string>
    <string name="product_recognition_desc">Milhares de\nprodutos</string>

    <!-- Text Recognition -->
    <string name="text_recognition">Reconhecimento de Texto</string>
    <string name="text_recognition_desc">Extração precisa\nde texto</string>

    <string name="history_empty_text">Nenhum registro histórico</string>
    <string name="recognition_result">Resultado do Reconhecimento</string>
    <string name="choose_browser">Por favor, selecione um navegador</string>
    <string name="baidu_url">https://pt.m.wikipedia.org/wiki/%s</string>

    <string name="possible_recognition_result">O resultado do reconhecimento pode ser:</string>
    <string name="encyclopedia">Enciclopédia</string>

    <!-- New strings for Image2TxtResultAct -->
    <string name="copy_success">Cópia bem-sucedida!</string>

    <!-- Format strings for displaying recognition results -->
    <string name="bank_info_format">Banco: %1$s\nNúmero do Cartão Bancário: %2$s</string>

    <string name="id_info_format">Nome: %1$s\nNúmero de ID: %2$s\nGênero: %3$s\nData de Nascimento: %4$s\nEtnia: %5$s\nEndereço: %6$s</string>

    <string name="license_info_format">Endereço: %1$s\nTipos de Veículo Permitidos: %2$s\nVálido Até: %3$s\nData de Nascimento: %4$s\nNúmero da Licença: %5$s\nEndereço: %6$s\nData da Primeira Emissão: %7$s\nNacionalidade: %8$s\nTipos de Veículo Permitidos: %9$s\nGênero: %10$s\nPeríodo de Validade: %11$s</string>

    <!-- Album text -->
    <string name="album">Álbum</string>
    <!-- Flash text -->
    <string name="flash_on">Flash ligado</string>
    <string name="flash_off">Flash desligado</string>

    <!-- Permission related messages -->
    <string name="permission_storage_denied">Por favor, permita o acesso ao armazenamento</string>
    <string name="permission_storage_rationale">A permissão de armazenamento é necessária para selecionar imagens. Permitir?</string>
    <string name="permission_media_rationale">A permissão de mídia é necessária para acessar suas fotos. Permitir?</string>
    <string name="permission_camera_denied">Por favor, habilite a permissão da câmera!</string>

    <!-- Dialog buttons -->
    <string name="cancel">Cancelar</string>
    <string name="allow">Permitir</string>
    <string name="confirm">Confirmar</string>

    <string name="exit_dialog_title">Aviso</string>
    <string name="exit_dialog_message">Tem certeza que deseja sair?</string>
    <string name="exit_dialog_positive">Sim</string>

    <string name="share_app_message">Baixe agora na loja de aplicativos!</string>
    <string name="share_app_title">Compartilhar App</string>
    <string name="cannot_open_store">Não foi possível abrir a loja de aplicativos</string>

    <string name="service_agreement">Termos de Serviço</string>
    <string name="privacy_policy_title">Política de Privacidade</string>
    <string name="read_more">Ler</string>
    <string name="and">e</string>
    <string name="period">.</string>
    <string name="btn_cancel">Cancelar</string>
    <string name="btn_agree">Concordar</string>
    <string name="welcome_app">Bem-vindo ao APP</string>
    <string name="privacy_policy">Política de Privacidade</string>
    <string name="btn_agree_and_continue">Concordar</string>
    <string name="btn_disagree_and_exit">Discordar e Sair</string>

    <!-- AI Recognition Prompts -->
    <!-- OCR Document Recognition -->
    <string name="prompt_ocr_doc">Por favor, reconheça todo o conteúdo de texto na imagem e gere a saída em formato Markdown, mantendo rigorosamente o layout, formato e estrutura originais.\n\n## Requisitos de Reconhecimento\n1. **Extração precisa**: Todo o texto visível, incluindo pontuação e caracteres especiais\n2. **Preservar estrutura**: Parágrafos de texto originais, indentação, listas e estrutura de tabelas\n3. **Ordem de leitura**: Texto de múltiplas colunas reconhecido da esquerda para a direita, de cima para baixo\n4. **Texto de gráficos**: Incluir texto em gráficos ou gráficas\n5. **Tratamento de conteúdo vazio**: Se não houver conteúdo reconhecível, retornar \"## Resultado do Reconhecimento\\n\\nNenhum conteúdo foi reconhecido\"\n6. **Anotação de desfoque**: Para texto pouco claro, marcar como `[Desfocado]`\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# Resultado do Reconhecimento de Texto\", seguido do conteúdo de texto reconhecido.</string>

    <!-- Generic Recognition -->
    <string name="prompt_generic">Por favor, reconheça o conteúdo principal na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão os objetos principais, cenas ou conteúdo na imagem\n- Fornecer descrições detalhadas e informações relacionadas\n- Se for um item, explicar seus usos, características, etc.\n- Se for uma cena, descrever o ambiente, atmosfera, etc.\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# Resultado do Reconhecimento de Imagem\", incluindo seções de conteúdo principal, descrição detalhada e informações relacionadas.</string>

    <!-- Animal Recognition -->
    <string name="prompt_animal">Por favor, reconheça o animal na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão as espécies animais\n- Fornecer informações de classificação científica\n- Descrever características morfológicas e hábitos de vida\n- Incluir informações importantes como status de conservação\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# 🐾 Resultado do Reconhecimento de Animal\", incluindo seções de informações básicas, informações taxonômicas, características morfológicas, habitat, hábitos de vida, área de distribuição, métodos de reprodução, status de conservação e significado cultural.</string>

    <!-- Plant Recognition -->
    <string name="prompt_plant">Por favor, reconheça a planta na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão as espécies de plantas\n- Fornecer classificação científica e características morfológicas\n- Incluir informações de cultivo e uso\n- Descrever valor ecológico e significado cultural\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# 🌿 Resultado do Reconhecimento de Planta\", incluindo seções de informações básicas, informações taxonômicas, características morfológicas, ambiente de crescimento, faixa de distribuição, técnicas de cultivo, valor de uso e significado cultural.</string>

    <!-- Fruit Recognition -->
    <string name="prompt_fruit">Por favor, reconheça a fruta na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão espécies e variedades de frutas\n- Fornecer informações de composição nutricional e calorias\n- Incluir recomendações de compra e armazenamento\n- Descrever sabor e métodos de consumo\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# 🍎 Resultado do Reconhecimento de Fruta\", incluindo seções de informações básicas, informações nutricionais, características de qualidade, recomendações de compra, métodos de armazenamento, recomendações de consumo e benefícios para a saúde.</string>

    <!-- Dish Recognition -->
    <string name="prompt_dish">Por favor, reconheça o prato na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão o nome e tipo do prato\n- Fornecer informações nutricionais e de calorias\n- Incluir métodos de preparação e ingredientes\n- Descrever contexto cultural e características\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# 🍽️ Resultado do Reconhecimento de Prato\", incluindo seções de informações básicas, informações nutricionais, características de sabor, ingredientes principais, métodos de preparação, contexto cultural, grupos adequados e valor nutricional.</string>

    <!-- Logo Recognition -->
    <string name="prompt_logo">Por favor, reconheça o logotipo da marca na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão o nome da marca e logotipo\n- Fornecer informações da empresa e indústria\n- Descrever características e significados do design\n- Incluir história da marca e influência\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# 🏷️ Resultado do Reconhecimento de Logotipo de Marca\", incluindo seções de informações básicas da marca, informações da empresa, análise de design do logotipo, informações da marca, influência do mercado, história de desenvolvimento e responsabilidade social.</string>

    <!-- Landmark Recognition -->
    <string name="prompt_landmark">Por favor, reconheça o edifício marco na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão o nome do edifício marco\n- Fornecer informações históricas e arquitetônicas\n- Descrever valor cultural e turístico\n- Incluir informações práticas de visita\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# 🏛️ Resultado do Reconhecimento de Edifício Marco\", incluindo seções de informações básicas, informações arquitetônicas, contexto histórico, características arquitetônicas, usos funcionais, valor cultural, status de proteção, informações turísticas e fatos interessantes.</string>

    <!-- Currency Recognition -->
    <string name="prompt_currency">Por favor, reconheça a moeda na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão o tipo e denominação da moeda\n- Fornecer informações do país emissor e ano\n- Descrever padrões de design e características de segurança\n- Incluir contexto histórico e cultural\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# 💰 Resultado do Reconhecimento de Moeda\", incluindo seções de informações básicas, informações de emissão, características de design, características de segurança, cultura histórica, informações econômicas e fatos interessantes.</string>

    <!-- Default Recognition -->
    <string name="prompt_default">Por favor, reconheça o conteúdo principal na imagem e gere informações detalhadas em formato Markdown.\n\n## Requisitos de Reconhecimento\n- Identificar com precisão os objetos principais ou conteúdo na imagem\n- Fornecer descrições detalhadas e informações relacionadas\n- Incluir conhecimento prático de fundo\n- Garantir precisão e confiabilidade das informações\n\nPor favor, gere a saída diretamente em formato Markdown, começando com \"# 🔍 Resultado do Reconhecimento de Imagem\", incluindo seções de conteúdo reconhecido, informações detalhadas, descrição de características e conhecimento relacionado.</string>

    <!-- Error Messages -->
    <string name="error_image_empty">A imagem não pode estar vazia</string>
    <string name="error_request_failed">Solicitação falhou: %s</string>
    <string name="error_http_error">Erro HTTP: %d</string>
    <string name="error_response_empty">O corpo da resposta está vazio</string>
    <string name="error_stream_abnormal_end">O fluxo de resposta terminou anormalmente</string>
    <string name="error_read_stream_failed">Falha ao ler o fluxo de resposta: %s</string>

    <!-- UI Messages -->
    <string name="detecting_image">Detectando imagem</string>
</resources>