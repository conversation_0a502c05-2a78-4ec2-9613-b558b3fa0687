<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="permission_request_denied">권한 요청이 거부되었습니다</string>
    <string name="error_gif">x 또는 y 크기는 적어도 %1$dpx여야 하며 파일 크기는 %2$sM을 초과해서는 안 됩니다</string>
    <string name="menu_scan_image">이미지 인식</string>
    <string name="menu_history">기록</string>
    <string name="menu_mine">내 정보</string>
    <string name="tab_home">홈</string>
    <string name="tab_fav">팁</string>
    <string name="tab_mine">내 정보</string>
    <string name="tab_shop">인화</string>
    <string name="tab_news">뉴스</string>
    <string name="tab_scan_pic">이미지 인식</string>
    <string name="tab_scan_word">정확하게</string>
    <string name="history" tools:ignore="ExtraTranslation">히스토리</string>
    <string name="about_us">회사 소개</string>
    <string name="check_update">업데이트 확인</string>
    <string name="feedback">피드백</string>
    <string name="clear_cache">캐시 지우기</string>
    <string name="private_policy">개인정보 보호정책</string>

    <string name="last_version">이미 최신 버전입니다</string>
    <string name="no_market">앱 마켓이 설치되어 있지 않습니다</string>
    <string name="clear_history">캐시가 지워졌습니다</string>
    <string name="qq">문의하기: <EMAIL></string>
    <string name="version">현재 버전: v</string>
    <string name="str_feedback_content">귀중한 의견을 제공해 주세요</string>
    <string name="str_feedback_qq">연락처(QQ)</string>
    <string name="submit_tips">피드백 내용 또는 연락처가 비어 있습니다</string>
    <string name="submit_success">제출 성공</string>
    <string name="submit">제출</string>

    <string name="take_photo_skills">촬영 팁</string>
    <string name="tabSegment_item_1_title">비자 사진 규격</string>
    <string name="tabSegment_item_2_title">증명사진 규격</string>
    <string name="tabSegment_pre_1_title">디지털 사진</string>
    <string name="tabSegment_pre_2_title">레이아웃 사진</string>
    <string name="app_slogn">스마트 촬영 정확한 이미지 인식</string>
    <string name="again_info">사용자의 개인 정보를 엄격히 보호합니다. 사용자가 계약에 동의하지 않으면 앱의 일부 기능이 제한될 수 있습니다.</string>
    <string name="share_other">다른 사람에게 추천</string>
    <string name="good_rate">좋은 평가 남기기</string>
    <string name="content">이 앱은 모든 사용자의 개인 정보를 존중하고 보호합니다. 더 정확하고 개인화된 서비스를 제공하기 위해 개인정보 보호정책에 따라 귀하의 개인 정보를 사용하고 공개합니다.</string>

    <!-- 일반 -->
    <string name="app_name">식물 인식</string>

    <!-- 상단 바 -->
    <string name="home_title">홈</string>

    <!-- 사진 촬영 인식 부분 -->
    <string name="take_photo_title">사진 촬영 인식</string>
    <string name="take_photo_desc">원터치 사진 인식</string>

    <!-- 앨범 이미지 인식 부분 -->
    <string name="gallery_photo_title">앨범 이미지 인식</string>
    <string name="gallery_photo_desc">로컬 이미지 자유 인식</string>

    <!-- 정밀 인식 제목 -->
    <string name="precise_recognition">정밀 인식</string>

    <!-- 식물 인식 -->
    <string name="plant_recognition">식물 인식</string>
    <string name="plant_recognition_desc">2만 종 이상의 식물 인식</string>

    <!-- 동물 인식 -->
    <string name="animal_recognition">동물 인식</string>
    <string name="animal_recognition_desc">8천 종 이상의 동물 인식</string>

    <!-- 꽃 인식 -->
    <string name="flower_recognition">꽃 인식</string>
    <string name="flower_recognition_desc">만 종 이상의 꽃 인식</string>

    <!-- 과일 채소 인식 -->
    <string name="fruit_veg_recognition">과일 채소 인식</string>
    <string name="fruit_veg_recognition_desc">천 종의 과일 채소 인식</string>

    <!-- 브랜드 로고 인식 -->
    <string name="logo_recognition">브랜드 로고 인식</string>
    <string name="logo_recognition_desc">만 종 이상의 로고 인식</string>

    <!-- 요리 인식 -->
    <string name="dish_recognition">요리 인식</string>
    <string name="dish_recognition_desc">천 종에 가까운 요리 인식</string>

    <!-- 랜드마크 인식 -->
    <string name="landmark_recognition">랜드마크 인식</string>
    <string name="landmark_recognition_desc">세계 유명 랜드마크 건물</string>

    <!-- 화폐 인식 -->
    <string name="currency_recognition">화폐 인식</string>
    <string name="currency_recognition_desc">세계 각국 화폐</string>

    <!-- 상품 인식 -->
    <string name="product_recognition">상품 인식</string>
    <string name="product_recognition_desc">만 종의 상품 인식</string>

    <!-- 텍스트 인식 -->
    <string name="text_recognition">텍스트 인식</string>
    <string name="text_recognition_desc">정확한 텍스트 추출</string>

    <string name="history_empty_text">기록이 비어 있습니다</string>

    <string name="recognition_result">인식 결과</string>
    <string name="choose_browser">브라우저를 선택하세요</string>
    <string name="baidu_url">https://ko.m.wikipedia.org/wiki/%s</string>
    <string name="possible_recognition_result">인식 결과는 다음과 같을 수 있습니다:</string>
    <string name="encyclopedia">백과사전</string>
    <string name="copy_success">복사 성공!</string>

    <!-- 인식 결과 표시를 위한 형식 문자열 -->
    <string name="bank_info_format">은행: %1$s\n카드 번호: %2$s</string>
    <string name="id_info_format">이름: %1$s\n신분증 번호: %2$s\n성별: %3$s\n생년월일: %4$s\n민족: %5$s\n주소: %6$s</string>
    <string name="license_info_format">주소: %1$s\n운전 가능 차종: %2$s\n만료일: %3$s\n생년월일: %4$s\n면허 번호: %5$s\n주소: %6$s\n최초 면허 취득일: %7$s\n국적: %8$s\n운전 가능 차종: %9$s\n성별: %10$s\n유효 기간: %11$s</string>

    <!-- 앨범 텍스트 -->
    <string name="album">앨범</string>
    <!-- 플래시 텍스트 -->
    <string name="flash_on">플래시 켜기</string>
    <string name="flash_off">플래시 끄기</string>

    <!-- 권한 관련 메시지 -->
    <string name="permission_storage_denied">관련 권한을 허용해 주세요</string>
    <string name="permission_storage_rationale">사진을 선택하려면 저장소 권한이 필요합니다. 허용하시겠습니까?</string>
    <string name="permission_media_rationale">사진을 선택하려면 사진 읽기 권한이 필요합니다. 허용하시겠습니까?</string>
    <string name="permission_camera_denied">카메라 권한을 열어주세요!</string>

    <!-- 대화상자 버튼 -->
    <string name="cancel">허용 안 함</string>
    <string name="allow">허용</string>
    <string name="confirm">확인</string>

    <!-- Exit Dialog -->
    <string name="exit_dialog_title">알림</string>
    <string name="exit_dialog_message">정말 종료하시겠습니까?</string>
    <string name="exit_dialog_positive">예</string>

    <string name="share_app_message">지금 앱 스토어에서 다운로드하세요!</string>
    <string name="share_app_title">앱 공유</string>
    <string name="cannot_open_store">앱 스토어를 열 수 없습니다</string>


    <string name="service_agreement">서비스 약관</string>
    <string name="privacy_policy_title">개인정보 처리방침</string>
    <string name="read_more">읽기</string>
    <string name="and">및</string>
    <string name="period">.</string>
    <string name="btn_cancel">취소</string>
    <string name="btn_agree">동의</string>
    <string name="welcome_app">앱에 오신 것을 환영합니다</string>
    <string name="privacy_policy">개인정보 처리방침</string>
    <string name="btn_agree_and_continue">동의</string>
    <string name="btn_disagree_and_exit">동의하지 않고 종료</string>

    <!-- AI Recognition Prompts -->
    <!-- OCR Document Recognition -->
    <string name="prompt_ocr_doc">이미지의 모든 텍스트 내용을 인식하고 원본 레이아웃, 형식 및 구조를 엄격히 유지하여 Markdown 형식으로 출력해 주세요.\n\n## 인식 요구사항\n1. **정확한 추출**: 구두점과 특수 문자를 포함한 모든 가시 텍스트\n2. **구조 보존**: 원본 텍스트의 단락, 들여쓰기, 목록 및 표 구조\n3. **읽기 순서**: 다중 열 텍스트는 왼쪽에서 오른쪽, 위에서 아래 순서로 인식\n4. **차트 텍스트**: 차트나 그래픽의 텍스트도 포함\n5. **빈 내용 처리**: 인식 가능한 내용이 없으면 \"## 인식 결과\\n\\n내용이 인식되지 않았습니다\" 반환\n6. **흐림 주석**: 불분명한 텍스트는 `[흐림]`으로 표시\n\nMarkdown 형식으로 직접 출력하고, \"# 텍스트 인식 결과\"로 시작하여 인식된 텍스트 내용을 이어주세요.</string>

    <!-- Generic Recognition -->
    <string name="prompt_generic">이미지의 주요 내용을 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 이미지의 주요 객체, 장면 또는 내용을 정확히 식별\n- 상세한 설명과 관련 정보 제공\n- 물품인 경우 용도, 특징 등을 설명\n- 장면인 경우 환경, 분위기 등을 묘사\n\nMarkdown 형식으로 직접 출력하고, \"# 이미지 인식 결과\"로 시작하여 주요 내용, 상세 설명, 관련 정보 섹션을 포함해 주세요.</string>

    <!-- Animal Recognition -->
    <string name="prompt_animal">이미지의 동물을 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 동물 종을 정확히 식별\n- 과학적 분류 정보 제공\n- 형태적 특징과 생활 습성 기술\n- 보호 상태 등 중요 정보 포함\n\nMarkdown 형식으로 직접 출력하고, \"# 🐾 동물 인식 결과\"로 시작하여 기본 정보, 분류학 정보, 형태적 특징, 서식지, 생활 습성, 분포 지역, 번식 방법, 보호 상태, 문화적 의미 섹션을 포함해 주세요.</string>

    <!-- Plant Recognition -->
    <string name="prompt_plant">이미지의 식물을 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 식물 종을 정확히 식별\n- 과학적 분류와 형태적 특징 제공\n- 재배 및 용도 정보 포함\n- 생태학적 가치와 문화적 의미 기술\n\nMarkdown 형식으로 직접 출력하고, \"# 🌿 식물 인식 결과\"로 시작하여 기본 정보, 분류학 정보, 형태적 특징, 성장 환경, 분포 범위, 재배 기술, 용도 가치, 문화적 의미 섹션을 포함해 주세요.</string>

    <!-- Fruit Recognition -->
    <string name="prompt_fruit">이미지의 과일을 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 과일 종류와 품종을 정확히 식별\n- 영양 성분과 칼로리 정보 제공\n- 구매 및 보관 권장사항 포함\n- 맛과 섭취 방법 기술\n\nMarkdown 형식으로 직접 출력하고, \"# 🍎 과일 인식 결과\"로 시작하여 기본 정보, 영양 정보, 품질 특성, 구매 권장사항, 보관 방법, 섭취 권장사항, 건강 효과 섹션을 포함해 주세요.</string>

    <!-- Dish Recognition -->
    <string name="prompt_dish">이미지의 요리를 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 요리명과 종류를 정확히 식별\n- 영양 및 칼로리 정보 제공\n- 조리 방법과 재료 포함\n- 문화적 배경과 특징 기술\n\nMarkdown 형식으로 직접 출력하고, \"# 🍽️ 요리 인식 결과\"로 시작하여 기본 정보, 영양 정보, 맛 특성, 주요 재료, 조리 방법, 문화적 배경, 적합 그룹, 영양 가치 섹션을 포함해 주세요.</string>

    <!-- Logo Recognition -->
    <string name="prompt_logo">이미지의 브랜드 로고를 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 브랜드명과 로고를 정확히 식별\n- 기업 및 업계 정보 제공\n- 디자인 특징과 의미 기술\n- 브랜드 역사와 영향력 포함\n\nMarkdown 형식으로 직접 출력하고, \"# 🏷️ 브랜드 로고 인식 결과\"로 시작하여 브랜드 기본 정보, 기업 정보, 로고 디자인 분석, 브랜드 정보, 시장 영향력, 발전 역사, 사회적 책임 섹션을 포함해 주세요.</string>

    <!-- Landmark Recognition -->
    <string name="prompt_landmark">이미지의 랜드마크 건물을 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 랜드마크 건물명을 정확히 식별\n- 역사 및 건축 정보 제공\n- 문화 및 관광 가치 기술\n- 실용적인 방문 정보 포함\n\nMarkdown 형식으로 직접 출력하고, \"# 🏛️ 랜드마크 건물 인식 결과\"로 시작하여 기본 정보, 건축 정보, 역사적 배경, 건축 특징, 기능 용도, 문화 가치, 보호 상태, 관광 정보, 흥미로운 사실 섹션을 포함해 주세요.</string>

    <!-- Currency Recognition -->
    <string name="prompt_currency">이미지의 화폐를 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 화폐 종류와 액면가를 정확히 식별\n- 발행국과 연도 정보 제공\n- 디자인 패턴과 보안 기능 기술\n- 역사 및 문화적 배경 포함\n\nMarkdown 형식으로 직접 출력하고, \"# 💰 화폐 인식 결과\"로 시작하여 기본 정보, 발행 정보, 디자인 특징, 보안 기능, 역사 문화, 경제 정보, 흥미로운 사실 섹션을 포함해 주세요.</string>

    <!-- Default Recognition -->
    <string name="prompt_default">이미지의 주요 내용을 인식하고 Markdown 형식으로 상세 정보를 출력해 주세요.\n\n## 인식 요구사항\n- 이미지의 주요 객체나 내용을 정확히 식별\n- 상세한 설명과 관련 정보 제공\n- 실용적인 배경 지식 포함\n- 정보의 정확성과 신뢰성 보장\n\nMarkdown 형식으로 직접 출력하고, \"# 🔍 이미지 인식 결과\"로 시작하여 인식된 내용, 상세 정보, 특징 기술, 관련 지식 섹션을 포함해 주세요.</string>

    <!-- Error Messages -->
    <string name="error_image_empty">이미지는 비어있을 수 없습니다</string>
    <string name="error_request_failed">요청 실패: %s</string>
    <string name="error_http_error">HTTP 오류: %d</string>
    <string name="error_response_empty">응답 본문이 비어있습니다</string>
    <string name="error_stream_abnormal_end">응답 스트림이 비정상적으로 종료되었습니다</string>
    <string name="error_read_stream_failed">응답 스트림 읽기 실패: %s</string>

    <!-- UI Messages -->
    <string name="detecting_image">이미지 감지 중</string>
</resources>