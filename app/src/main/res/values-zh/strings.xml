<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="permission_request_denied">Permission request denied</string>
    <string name="error_gif">x or y bound size should be at least %1$dpx and file size should be no more than %2$sM</string>
    <string name="menu_scan_image">识图</string>
    <string name="menu_history">历史</string>
    <string name="menu_mine">我的</string>
    <string name="tab_home">首页</string>
    <string name="tab_fav">技巧</string>
    <string name="tab_mine">我的</string>
    <string name="tab_shop">冲印</string>
    <string name="tab_news">资讯</string>
    <string name="tab_scan_pic">识图</string>
    <string name="tab_scan_word">精准</string>
    <string name="history" tools:ignore="ExtraTranslation">历史记录</string>
    <string name="about_us">关于我们</string>
    <string name="check_update">检查更新</string>
    <string name="feedback">意见反馈</string>
    <string name="clear_cache">清空缓存</string>
    <string name="private_policy">隐私与政策</string>

    <string name="last_version">已经是最新版本啦</string>
    <string name="no_market">您没有安装应用市场</string>
    <string name="clear_history">已清除缓存</string>
    <string name="qq">联系我们：<EMAIL></string>
    <string name="version">当前版本: v</string>
    <string name="str_feedback_content">提出您的宝贵意见吧</string>
    <string name="str_feedback_qq">联系方式（QQ）</string>
    <string name="submit_tips">您的反馈内容或联系方式为空</string>
    <string name="submit_success">提交成功</string>
    <string name="submit">提交</string>

    <string name="take_photo_skills">拍摄技巧</string>
    <string name="tabSegment_item_1_title">签证照规格</string>
    <string name="tabSegment_item_2_title">证件照规格</string>
    <string name="tabSegment_pre_1_title">电子照</string>
    <string name="tabSegment_pre_2_title">排版照</string>
    <string name="app_slogn">智能拍照精准识图</string>
    <string name="again_info"> 我们会严格保护用户的隐私信息，如若用户不同意协议 我们APP的部分功能可能会受限。 </string>
    <string name="share_other">推荐给他人</string>
    <string name="good_rate">给个好评</string>
    <string name="content">本应用尊重并保护所有用户的个人隐私权。为了给您提供更精、更个性化的服务，本应用会按照隐私政策的规定使用和披露您的个人信息。</string>
        <!-- 通用 -->
        <string name="app_name">植物识别</string>

        <!-- 顶部栏 -->
        <string name="home_title">首页</string>

        <!-- 拍照识别部分 -->
        <string name="take_photo_title">拍照识别</string>
        <string name="take_photo_desc">一键拍照识别</string>

        <!-- 相册图片识别部分 -->
        <string name="gallery_photo_title">相册图片识别</string>
        <string name="gallery_photo_desc">本地图片自由识别</string>

        <!-- 精准识别标题 -->
        <string name="precise_recognition">精准识别</string>

        <!-- 植物识别 -->
        <string name="plant_recognition">植物识别</string>
        <string name="plant_recognition_desc">识别超2万种植物</string>

        <!-- 动物识别 -->
        <string name="animal_recognition">动物识别</string>
        <string name="animal_recognition_desc">识别超8千种动物</string>

        <!-- 花卉识别 -->
        <string name="flower_recognition">花卉识别</string>
        <string name="flower_recognition_desc">识别上万种花卉</string>

        <!-- 果蔬识别 -->
        <string name="fruit_veg_recognition">果蔬识别</string>
        <string name="fruit_veg_recognition_desc">识别千种水果蔬菜</string>

        <!-- 品牌logo识别 -->
        <string name="logo_recognition">品牌logo识别</string>
        <string name="logo_recognition_desc">识别超过万种logo</string>

        <!-- 菜品识别 -->
        <string name="dish_recognition">菜品识别</string>
        <string name="dish_recognition_desc">识别近千种菜品</string>

        <!-- 地标识别 -->
        <string name="landmark_recognition">地标识别</string>
        <string name="landmark_recognition_desc">世界知名地标建筑</string>

        <!-- 货币识别 -->
        <string name="currency_recognition">货币识别</string>
        <string name="currency_recognition_desc">世界各国货币</string>

        <!-- 商品识别 -->
        <string name="product_recognition">商品识别</string>
        <string name="product_recognition_desc">万种商品识别</string>

        <!-- 文字识别 -->
        <string name="text_recognition">文字识别</string>
        <string name="text_recognition_desc">精准提取文字</string>
    <string name="history_empty_text">历史记录为空</string>

    <string name="recognition_result">识别结果</string>
    <string name="choose_browser">请选择浏览器</string>
    <string name="baidu_url">https://zh.m.wikipedia.org/wiki/%s</string>
    <string name="possible_recognition_result">识别结果可能是：</string>
    <string name="encyclopedia">百科</string>
    <string name="copy_success">复制成功！</string>
    <!-- Format strings for displaying recognition results -->
    <string name="bank_info_format">银行：%1$s\n银行卡号：%2$s</string>
    <string name="id_info_format">姓名: %1$s\n身份证号: %2$s\n性别: %3$s\n出生: %4$s\n民族: %5$s\n住址: %6$s</string>
    <string name="license_info_format">住址: %1$s\n准驾车型: %2$s\n至: %3$s\n出生日期: %4$s\n证号: %5$s\n住址: %6$s\n初次领证日期: %7$s\n国籍: %8$s\n准驾车型: %9$s\n性别: %10$s\n有效期限: %11$s</string>
    <!-- Album text -->
    <string name="album">相册</string>
    <!-- Flash text -->
    <string name="flash_on">开闪光</string>
    <string name="flash_off">关闪光</string>


    <!-- Permission related messages -->
    <string name="permission_storage_denied">请允许获取相关授权</string>
    <string name="permission_storage_rationale">选择图片需要获取您存储权限是否允许？</string>
    <string name="permission_media_rationale">选择图片需要获取您的读取相册权限是否允许？</string>
    <string name="permission_camera_denied">请打开相机权限!</string>

    <!-- Dialog buttons -->
    <string name="cancel">不允许</string>
    <string name="allow">允许</string>
    <string name="confirm">确定</string>

    <string name="exit_dialog_title">温馨提示</string>
    <string name="exit_dialog_message">确定要退出么？</string>
    <string name="exit_dialog_positive">是的</string>
    <string name="share_app_message">赶紧去应用市场下载体验吧！</string>
    <string name="share_app_title">App分享</string>
    <string name="cannot_open_store">无法打开应用商店</string>

<!--    <string name="welcome_app">Welcome to APP</string>-->
<!--    <string name="privacy_policy">Privacy Policy</string>-->
<!--    <string name="btn_agree_and_continue">Agree</string>-->
<!--    <string name="btn_disagree_and_exit">Disagree and Exit</string>-->

<!--    <string name="service_agreement">服务协议</string>-->
<!--    <string name="privacy_policy_title">隐私政策</string>-->
<!--    <string name="read_more">阅读</string>-->
<!--    <string name="and">和</string>-->
<!--    <string name="period">.</string>-->
<!--    <string name="btn_cancel">取消</string>-->
<!--    <string name="btn_agree">同意</string>-->


    <string name="service_agreement">服务协议</string>
    <string name="privacy_policy_title">隐私政策</string>
    <string name="read_more">阅读</string>
    <string name="and">和</string>
    <string name="period">.</string>
    <string name="btn_cancel">取消</string>
    <string name="btn_agree">同意</string>
    <string name="welcome_app">欢迎来到APP</string>
    <string name="privacy_policy">隐私政策</string>
    <string name="btn_agree_and_continue">同意</string>
    <string name="btn_disagree_and_exit">不同意并退出</string>

    <!-- AI Recognition Prompts -->
    <!-- OCR Document Recognition -->
    <string name="prompt_ocr_doc">请识别图片中的所有文本内容，并以Markdown格式输出，严格保持原有的排版、格式和布局结构。\n\n## 识别要求\n1. **准确提取**：所有可见文字，包括标点符号和特殊字符\n2. **保留结构**：原始文本的段落分隔、缩进、列表和表格结构\n3. **阅读顺序**：多列文本按从左到右、从上到下的顺序识别\n4. **图表文字**：包含图表或图形中的文字\n5. **空内容处理**：若无可识别内容，返回\"## 识别结果\\n\\n未识别到内容\"\n6. **模糊标注**：对于模糊不清的文字，标注为`[模糊]`\n\n请直接以Markdown格式输出，以\"# 文本识别结果\"开头，然后是识别到的文本内容。</string>

    <!-- Generic Recognition -->
    <string name="prompt_generic">请识别图片中的主要内容并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别图片中的主要物体、场景或内容\n- 提供详细的描述和相关信息\n- 如果是物品，说明其用途、特征等\n- 如果是场景，描述环境、氛围等\n\n请直接以Markdown格式输出，以\"# 图像识别结果\"开头，包含主要内容、详细描述和相关信息等部分。</string>

    <!-- Animal Recognition -->
    <string name="prompt_animal">请识别图片中的动物并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别动物种类\n- 提供科学分类信息\n- 描述形态特征和生活习性\n- 包含保护状态等重要信息\n\n请直接以Markdown格式输出，以\"# 🐾 动物识别结果\"开头，包含基本信息、分类学信息、形态特征、栖息环境、生活习性、分布区域、繁殖方式、保护状态和文化意义等部分。</string>

    <!-- Plant Recognition -->
    <string name="prompt_plant">请识别图片中的植物并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别植物种类\n- 提供科学分类和形态特征\n- 包含栽培和用途信息\n- 描述生态价值和文化意义\n\n请直接以Markdown格式输出，以\"# 🌿 植物识别结果\"开头，包含基本信息、分类学信息、形态特征、生长环境、分布范围、栽培技术、用途价值和文化意义等部分。</string>

    <!-- Fruit Recognition -->
    <string name="prompt_fruit">请识别图片中的水果并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别水果种类和品种\n- 提供营养成分和热量信息\n- 包含选购和储存建议\n- 描述口感和食用方法\n\n请直接以Markdown格式输出，以\"# 🍎 水果识别结果\"开头，包含基本信息、营养信息、品质特征、选购建议、储存方法、食用建议和健康功效等部分。</string>

    <!-- Dish Recognition -->
    <string name="prompt_dish">请识别图片中的菜品并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别菜品名称和类型\n- 提供营养和热量信息\n- 包含制作方法和食材\n- 描述文化背景和特色\n\n请直接以Markdown格式输出，以\"# 🍽️ 菜品识别结果\"开头，包含基本信息、营养信息、口味特征、主要食材、制作方法、文化背景、适宜人群和营养价值等部分。</string>

    <!-- Logo Recognition -->
    <string name="prompt_logo">请识别图片中的品牌Logo并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别品牌名称和Logo\n- 提供公司和行业信息\n- 描述设计特点和含义\n- 包含品牌历史和影响力\n\n请直接以Markdown格式输出，以\"# 🏷️ 品牌Logo识别结果\"开头，包含品牌基本信息、公司信息、Logo设计分析、品牌信息、市场影响力、发展历程和社会责任等部分。</string>

    <!-- Landmark Recognition -->
    <string name="prompt_landmark">请识别图片中的地标建筑并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别地标建筑名称\n- 提供历史和建筑信息\n- 描述文化和旅游价值\n- 包含实用的游览信息\n\n请直接以Markdown格式输出，以\"# 🏛️ 地标建筑识别结果\"开头，包含基本信息、建筑信息、历史背景、建筑特色、功能用途、文化价值、保护状态、旅游信息和有趣事实等部分。</string>

    <!-- Currency Recognition -->
    <string name="prompt_currency">请识别图片中的货币并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别货币类型和面额\n- 提供发行国家和年份信息\n- 描述设计图案和防伪特征\n- 包含历史和文化背景\n\n请直接以Markdown格式输出，以\"# 💰 货币识别结果\"开头，包含基本信息、发行信息、设计特征、防伪特征、历史文化、经济信息和有趣事实等部分。</string>

    <!-- Default Recognition -->
    <string name="prompt_default">请识别图片中的主要内容并以Markdown格式输出详细信息。\n\n## 识别要求\n- 准确识别图片中的主要物体或内容\n- 提供详细的描述和相关信息\n- 包含实用的背景知识\n- 确保信息准确可靠\n\n请直接以Markdown格式输出，以\"# 🔍 图像识别结果\"开头，包含识别内容、详细信息、特征描述和相关知识等部分。</string>

    <!-- Error Messages -->
    <string name="error_image_empty">图片不能为空</string>
    <string name="error_request_failed">请求失败: %s</string>
    <string name="error_http_error">HTTP 错误: %d</string>
    <string name="error_response_empty">响应体为空</string>
    <string name="error_stream_abnormal_end">响应流异常结束</string>
    <string name="error_read_stream_failed">读取响应流失败: %s</string>

    <!-- UI Messages -->
    <string name="detecting_image">图片检测中</string>
</resources>

