<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="permission_request_denied">権限リクエストが拒否されました</string>
    <string name="error_gif">xまたはyの境界サイズは少なくとも%1$dpxであり、ファイルサイズは%2$sMを超えないようにしてください</string>
    <string name="menu_scan_image">画像認識</string>
    <string name="menu_history">履歴</string>
    <string name="menu_mine">マイページ</string>
    <string name="tab_home">ホーム</string>
    <string name="tab_fav">テクニック</string>
    <string name="tab_mine">マイページ</string>
    <string name="tab_shop">プリント</string>
    <string name="tab_news">ニュース</string>
    <string name="tab_scan_pic">画像認識</string>
    <string name="tab_scan_word">精密</string>
    <string name="history" tools:ignore="ExtraTranslation">履歴</string>
    <string name="about_us">私たちについて</string>
    <string name="check_update">アップデートを確認</string>
    <string name="feedback">フィードバック</string>
    <string name="clear_cache">キャッシュをクリア</string>
    <string name="private_policy">プライバシーとポリシー</string>

    <string name="last_version">すでに最新バージョンです</string>
    <string name="no_market">アプリマーケットがインストールされていません</string>
    <string name="clear_history">キャッシュがクリアされました</string>
    <string name="qq">お問い合わせ: <EMAIL></string>
    <string name="version">現在のバージョン: v</string>
    <string name="str_feedback_content">貴重なご意見をお寄せください</string>
    <string name="str_feedback_qq">連絡先（QQ）</string>
    <string name="submit_tips">フィードバック内容または連絡先が空です</string>
    <string name="submit_success">送信成功</string>
    <string name="submit">送信</string>

    <string name="take_photo_skills">撮影テクニック</string>
    <string name="tabSegment_item_1_title">ビザ写真の規格</string>
    <string name="tabSegment_item_2_title">証明写真の規格</string>
    <string name="tabSegment_pre_1_title">デジタル写真</string>
    <string name="tabSegment_pre_2_title">レイアウト写真</string>
    <string name="app_slogn">スマート撮影、精密画像認識</string>
    <string name="again_info">ユーザーのプライバシー情報を厳守します。ユーザーが同意しない場合、アプリの一部機能が制限される場合があります。</string>
    <string name="share_other">他の人にお勧め</string>
    <string name="good_rate">評価する</string>
    <string name="content">このアプリケーションは、すべてのユーザーの個人情報を尊重し保護します。より正確で個性化されたサービスを提供するために、プライバシーポリシーに従ってお客様の個人情報を使用および開示します。</string>

    <!-- 一般 -->
    <string name="app_name">植物認識</string>

    <!-- トップバー -->
    <string name="home_title">ホーム</string>

    <!-- 撮影認識部分 -->
    <string name="take_photo_title">写真撮影認識</string>
    <string name="take_photo_desc">ワンタッチ写真認識</string>

    <!-- アルバム画像認識部分 -->
    <string name="gallery_photo_title">アルバム画像認識</string>
    <string name="gallery_photo_desc">ローカル画像自由認識</string>

    <!-- 精密認識タイトル -->
    <string name="precise_recognition">精密認識</string>

    <!-- 植物認識 -->
    <string name="plant_recognition">植物認識</string>
    <string name="plant_recognition_desc">2万種以上の植物を認識</string>

    <!-- 動物認識 -->
    <string name="animal_recognition">動物認識</string>
    <string name="animal_recognition_desc">8千種以上の動物を認識</string>

    <!-- 花の認識 -->
    <string name="flower_recognition">花の認識</string>
    <string name="flower_recognition_desc">1万種以上の花を認識</string>

    <!-- 果物・野菜認識 -->
    <string name="fruit_veg_recognition">果物・野菜認識</string>
    <string name="fruit_veg_recognition_desc">千種の果物と野菜を認識</string>

    <!-- ブランドロゴ認識 -->
    <string name="logo_recognition">ブランドロゴ認識</string>
    <string name="logo_recognition_desc">1万種以上のロゴを認識</string>

    <!-- 料理認識 -->
    <string name="dish_recognition">料理認識</string>
    <string name="dish_recognition_desc">千種近くの料理を認識</string>

    <!-- ランドマーク認識 -->
    <string name="landmark_recognition">ランドマーク認識</string>
    <string name="landmark_recognition_desc">世界の有名なランドマーク建築</string>

    <!-- 通貨認識 -->
    <string name="currency_recognition">通貨認識</string>
    <string name="currency_recognition_desc">世界各国の通貨</string>

    <!-- 商品認識 -->
    <string name="product_recognition">商品認識</string>
    <string name="product_recognition_desc">1万種の商品を認識</string>

    <!-- テキスト認識 -->
    <string name="text_recognition">テキスト認識</string>
    <string name="text_recognition_desc">精密なテキスト抽出</string>

    <string name="history_empty_text">履歴は空です</string>

    <string name="recognition_result">認識結果</string>
    <string name="choose_browser">ブラウザを選択してください</string>
    <string name="baidu_url">https://ja.m.wikipedia.org/wiki/%s</string>
    <string name="possible_recognition_result">認識結果の可能性：</string>
    <string name="encyclopedia">百科事典</string>
    <string name="copy_success">コピー成功！</string>

    <!-- 認識結果表示のためのフォーマット文字列 -->
    <string name="bank_info_format">銀行：%1$s\nカード番号：%2$s</string>
    <string name="id_info_format">名前: %1$s\nID番号: %2$s\n性別: %3$s\n生年月日: %4$s\n民族: %5$s\n住所: %6$s</string>
    <string name="license_info_format">住所: %1$s\n運転可能車種: %2$s\n有効期限: %3$s\n生年月日: %4$s\n免許証番号: %5$s\n住所: %6$s\n初回取得日: %7$s\n国籍: %8$s\n運転可能車種: %9$s\n性別: %10$s\n有効期限: %11$s</string>

    <!-- アルバムテキスト -->
    <string name="album">アルバム</string>
    <!-- フラッシュテキスト -->
    <string name="flash_on">フラッシュオン</string>
    <string name="flash_off">フラッシュオフ</string>

    <!-- 権限関連メッセージ -->
    <string name="permission_storage_denied">関連する権限を許可してください</string>
    <string name="permission_storage_rationale">画像を選択するにはストレージ権限が必要です。許可しますか？</string>
    <string name="permission_media_rationale">画像を選択するにはアルバム読み取り権限が必要です。許可しますか？</string>
    <string name="permission_camera_denied">カメラ権限を開いてください！</string>

    <!-- ダイアログボタン -->
    <string name="cancel">許可しない</string>
    <string name="allow">許可する</string>
    <string name="confirm">確認</string>

    <!-- Exit Dialog -->
    <string name="exit_dialog_title">お知らせ</string>
    <string name="exit_dialog_message">本当に終了しますか？</string>
    <string name="exit_dialog_positive">はい</string>

    <string name="share_app_message">今すぐアプリストアからダウンロードしよう！</string>
    <string name="share_app_title">アプリを共有</string>
    <string name="cannot_open_store">アプリストアを開けません</string>



    <string name="service_agreement">サービス利用規約</string>
    <string name="privacy_policy_title">プライバシーポリシー</string>
    <string name="read_more">読む</string>
    <string name="and">と</string>
    <string name="period">。</string>
    <string name="btn_cancel">キャンセル</string>
    <string name="btn_agree">同意する</string>
    <string name="welcome_app">アプリへようこそ</string>
    <string name="privacy_policy">プライバシーポリシー</string>
    <string name="btn_agree_and_continue">同意する</string>
    <string name="btn_disagree_and_exit">同意しない・終了する</string>

    <!-- AI Recognition Prompts -->
    <!-- OCR Document Recognition -->
    <string name="prompt_ocr_doc">画像内のすべてのテキスト内容を認識し、元のレイアウト、フォーマット、構造を厳密に維持してMarkdown形式で出力してください。\n\n## 認識要件\n1. **正確な抽出**: 句読点や特殊文字を含むすべての可視テキスト\n2. **構造の保持**: 元のテキストの段落、インデント、リスト、表の構造\n3. **読み順**: 複数列のテキストは左から右、上から下の順序で認識\n4. **図表のテキスト**: チャートやグラフィック内のテキストも含める\n5. **空コンテンツの処理**: 認識可能なコンテンツがない場合は「## 認識結果\\n\\nコンテンツが認識されませんでした」を返す\n6. **ぼかし注釈**: 不明瞭なテキストには`[ぼかし]`とマーク\n\nMarkdown形式で直接出力し、「# テキスト認識結果」で始まり、認識されたテキスト内容を続けてください。</string>

    <!-- Generic Recognition -->
    <string name="prompt_generic">画像内の主要コンテンツを認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- 画像内の主要オブジェクト、シーン、またはコンテンツを正確に識別\n- 詳細な説明と関連情報を提供\n- アイテムの場合は、用途、特徴などを説明\n- シーンの場合は、環境、雰囲気などを描写\n\nMarkdown形式で直接出力し、「# 画像認識結果」で始まり、主要コンテンツ、詳細説明、関連情報のセクションを含めてください。</string>

    <!-- Animal Recognition -->
    <string name="prompt_animal">画像内の動物を認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- 動物種を正確に識別\n- 科学的分類情報を提供\n- 形態的特徴と生活習慣を記述\n- 保護状況などの重要情報を含める\n\nMarkdown形式で直接出力し、「# 🐾 動物認識結果」で始まり、基本情報、分類学情報、形態的特徴、生息地、生活習慣、分布地域、繁殖方法、保護状況、文化的意義のセクションを含めてください。</string>

    <!-- Plant Recognition -->
    <string name="prompt_plant">画像内の植物を認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- 植物種を正確に識別\n- 科学的分類と形態的特徴を提供\n- 栽培と用途情報を含める\n- 生態学的価値と文化的意義を記述\n\nMarkdown形式で直接出力し、「# 🌿 植物認識結果」で始まり、基本情報、分類学情報、形態的特徴、成長環境、分布範囲、栽培技術、用途価値、文化的意義のセクションを含めてください。</string>

    <!-- Fruit Recognition -->
    <string name="prompt_fruit">画像内の果物を認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- 果物の種類と品種を正確に識別\n- 栄養成分とカロリー情報を提供\n- 購入と保存の推奨事項を含める\n- 味と消費方法を記述\n\nMarkdown形式で直接出力し、「# 🍎 果物認識結果」で始まり、基本情報、栄養情報、品質特性、購入推奨、保存方法、消費推奨、健康効果のセクションを含めてください。</string>

    <!-- Dish Recognition -->
    <string name="prompt_dish">画像内の料理を認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- 料理名と種類を正確に識別\n- 栄養とカロリー情報を提供\n- 調理方法と材料を含める\n- 文化的背景と特徴を記述\n\nMarkdown形式で直接出力し、「# 🍽️ 料理認識結果」で始まり、基本情報、栄養情報、味の特徴、主要材料、調理方法、文化的背景、適合グループ、栄養価値のセクションを含めてください。</string>

    <!-- Logo Recognition -->
    <string name="prompt_logo">画像内のブランドロゴを認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- ブランド名とロゴを正確に識別\n- 企業と業界情報を提供\n- デザインの特徴と意味を記述\n- ブランドの歴史と影響力を含める\n\nMarkdown形式で直接出力し、「# 🏷️ ブランドロゴ認識結果」で始まり、ブランド基本情報、企業情報、ロゴデザイン分析、ブランド情報、市場影響力、発展歴史、社会的責任のセクションを含めてください。</string>

    <!-- Landmark Recognition -->
    <string name="prompt_landmark">画像内のランドマーク建築を認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- ランドマーク建築名を正確に識別\n- 歴史と建築情報を提供\n- 文化と観光価値を記述\n- 実用的な訪問情報を含める\n\nMarkdown形式で直接出力し、「# 🏛️ ランドマーク建築認識結果」で始まり、基本情報、建築情報、歴史的背景、建築特徴、機能用途、文化価値、保護状況、観光情報、興味深い事実のセクションを含めてください。</string>

    <!-- Currency Recognition -->
    <string name="prompt_currency">画像内の通貨を認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- 通貨の種類と額面を正確に識別\n- 発行国と年の情報を提供\n- デザインパターンとセキュリティ機能を記述\n- 歴史と文化的背景を含める\n\nMarkdown形式で直接出力し、「# 💰 通貨認識結果」で始まり、基本情報、発行情報、デザイン特徴、セキュリティ機能、歴史文化、経済情報、興味深い事実のセクションを含めてください。</string>

    <!-- Default Recognition -->
    <string name="prompt_default">画像内の主要コンテンツを認識し、Markdown形式で詳細情報を出力してください。\n\n## 認識要件\n- 画像内の主要オブジェクトまたはコンテンツを正確に識別\n- 詳細な説明と関連情報を提供\n- 実用的な背景知識を含める\n- 情報の正確性と信頼性を確保\n\nMarkdown形式で直接出力し、「# 🔍 画像認識結果」で始まり、認識されたコンテンツ、詳細情報、特徴記述、関連知識のセクションを含めてください。</string>

    <!-- Error Messages -->
    <string name="error_image_empty">画像を空にすることはできません</string>
    <string name="error_request_failed">リクエストが失敗しました: %s</string>
    <string name="error_http_error">HTTPエラー: %d</string>
    <string name="error_response_empty">レスポンスボディが空です</string>
    <string name="error_stream_abnormal_end">レスポンスストリームが異常終了しました</string>
    <string name="error_read_stream_failed">レスポンスストリームの読み取りに失敗しました: %s</string>

    <!-- UI Messages -->
    <string name="detecting_image">画像を検出中</string>
</resources>