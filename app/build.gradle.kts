plugins {
    id("com.android.application")
    id("kotlin-android")
    id("kotlin-parcelize")
    id("com.google.devtools.ksp")
    id("android-junk-code")
}

val packageName = "com.lanxtech.plantocr"
val appName = "植物识别"
val appOwner = "上海揽熙信息技术有限公司"
val junkPrefix = "lanxtech_"

//原始信息不要删除
//val packageName = "com.dicernszh.botany"
//val appName = "识植物"
//val appOwner = "南京俱盈网络科技有限公司"
//val junkPrefix = "dicernszh_"

// KSP configuration for RxHttp
ksp {
    arg("rxhttp_rxjava", "rxjava3")
}

android {
    namespace = "com.dicernszh.botany"
    compileSdk = 35
    
    // 首先定义签名配置
    signingConfigs {
        create("release") {
            storeFile = file(File(project.rootDir.absolutePath, "play_zwsb.jks").absolutePath)
            storePassword = "qwerasdf"
            keyAlias = "key0"
            keyPassword = "qwerasdf"
        }
        create("test") {
            storeFile = file(File(project.rootDir.absolutePath, "app_key.jks").absolutePath)
            storePassword = "qwerasdf"
            keyAlias = "key0"
            keyPassword = "qwerasdf"
        }
    }
    
    defaultConfig {
        applicationId = "com.lanxtech.plantocr"
        minSdk = 27
        targetSdk = 35
        versionCode = 1001
        versionName = "1.0.1"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += mapOf("rxhttp_rxjava" to "rxjava3")     //可传入rxjava2、rxjava3
            }
        }
        multiDexEnabled = true
    }
    
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            signingConfig = signingConfigs.getByName("release")
        }
        debug {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            signingConfig = signingConfigs.getByName("release")
        }
    }
    
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = "17"
    }
    
    lint {
        baseline = file("lint-baseline.xml")
        abortOnError = false
        checkReleaseBuilds = false
    }
    
    // 禁用单元测试，避免JUnit依赖问题
    testOptions {
        unitTests.isReturnDefaultValues = true
        unitTests {
            isIncludeAndroidResources = true
        }
    }
    
    flavorDimensions += "appstore"
    productFlavors {
        create("playstore") {
            dimension = "appstore"
            applicationId = packageName
            versionCode = 3005
            versionName = "3.0.5"
            manifestPlaceholders["icon"] = "@mipmap/ic_logo2"
            manifestPlaceholders["appName"] = appName
            buildConfigField("String", "APP_NAME", "\"${appName}\"")
            buildConfigField("String", "OWNER", "\"${appOwner}\"")
            buildConfigField("String", "UMENG_APP_KEY", "\"614ddfa466b59330aa6ffe26\"")
            buildConfigField("String", "UMENG_APP_CHANNEL", "\"playstore${appName}\"")
            buildConfigField("String", "CP_ID", "\"c1001\"")
            buildConfigField("String", "CH_ID", "\"huawei20191121\"")
        }
    }
}

//configurations.all {
//    resolutionStrategy {
//        force("androidx.core:core-ktx:1.3.1")
//    }
//}

// 应用junkcode.gradle文件来处理android-junk-code插件配置
apply(from = "junkcode.gradle")


dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.recyclerview:recyclerview:1.3.2")
    implementation("androidx.multidex:multidex:2.0.1")
    implementation("com.google.android.material:material:1.11.0")
    
    // Kotlin
    implementation("org.jetbrains.kotlin:kotlin-stdlib:2.0.0") // Update to match project Kotlin version
    
    // 图片选择器
    implementation("io.github.lucksiege:pictureselector:v3.11.1")
    implementation("io.github.lucksiege:compress:v3.11.1")
    
    // 图片加载
    implementation("com.github.bumptech.glide:glide:4.16.0")
    ksp("com.github.bumptech.glide:compiler:4.16.0")
    implementation("com.squareup.picasso:picasso:2.8")

    // Markdown渲染
    implementation("io.noties.markwon:core:4.6.2")
    implementation("io.noties.markwon:html:4.6.2")
    implementation("io.noties.markwon:image-glide:4.6.2")
    implementation("io.noties.markwon:linkify:4.6.2")
    
    // 相机
    implementation("com.otaliastudios:cameraview:2.7.2")
    
    // 工具库
    implementation("com.blankj:utilcodex:1.31.1")

//    implementation "com.squareup.okhttp3:okhttp:${okhttp_version}"
//    implementation "com.squareup.okhttp3:logging-interceptor:${okhttp_version}"
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.github.liujingxing.rxhttp:rxhttp:3.3.2")
    ksp("com.github.liujingxing.rxhttp:rxhttp-compiler:3.3.2")



    implementation("com.github.liujingxing.rxhttp:converter-fastjson:3.3.2")
    implementation("com.github.liujingxing.rxhttp:converter-jackson:3.3.2")
    implementation("com.github.liujingxing.rxhttp:converter-moshi:3.3.2")
    implementation("com.github.liujingxing.rxhttp:converter-protobuf:3.3.2")
    implementation("com.github.liujingxing.rxhttp:converter-simplexml:3.3.2")
    implementation("com.github.liujingxing.rxlife:rxlife-rxjava3:2.2.2")


    // 数据库
    implementation("com.j256.ormlite:ormlite-android:5.1")
    implementation("com.j256.ormlite:ormlite-core:5.1")
    
    // RxJava
    implementation("io.reactivex.rxjava3:rxjava:3.1.8")
    implementation("io.reactivex.rxjava3:rxandroid:3.0.2")
    implementation("io.reactivex:rxjava:1.3.8")
    implementation("io.reactivex:rxandroid:1.2.1")
    
    // QMUI
    implementation("com.qmuiteam:qmui:2.0.1")
    implementation("com.qmuiteam:arch:2.0.1")
    implementation("androidx.legacy:legacy-support-core-ui:1.0.0")
    
    // 友盟统计
//    implementation("com.umeng.umsdk:common:9.8.0")
//    implementation("com.umeng.umsdk:asms:1.8.6")
//    implementation("com.umeng.umsdk:apm:1.9.11")



    
    // 测试
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
   // implementation 'com.qq.e.union:union:4.551.1421'
//    implementation("com.android.support:multidex:1.0.3")

    //广点通
}
